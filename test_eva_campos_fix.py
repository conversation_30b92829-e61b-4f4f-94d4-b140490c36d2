#!/usr/bin/env python3
"""
Test Eva Campos Fix
==================
Test that the <PERSON> (hiring manager) name is no longer extracted as the candidate name.
"""

import glob
from hybrid_german_cv_parser import HybridGermanCVParser

def test_eva_campos_fix():
    """Test that <PERSON>s is not extracted as candidate name"""
    print("🔧 Testing Eva Campos Fix")
    print("=" * 50)
    
    parser = HybridGermanCVParser()
    
    # Find the Bewerbung.pdf file that contains Eva Campos
    cv_files = [f for f in glob.glob('uploads/*.pdf') if 'bewerbung.pdf' in f.lower()]
    
    if not cv_files:
        print("❌ No Bewerbung.pdf found")
        return
    
    cv_file = cv_files[0]
    filename = cv_file.split('\\')[-1]
    
    print(f"📄 Testing: {filename}")
    print("This CV likely contains '<PERSON> Campos' (hiring manager name)")
    
    # Test without candidate name (old behavior)
    print("\n🔍 Test 1: Without candidate name validation")
    try:
        result1 = parser.extract_cv_data_bilingual(cv_file, ['name'])
        extracted_name = result1.get('name', 'Not found')
        print(f"Extracted name: {extracted_name}")
        
        if 'eva campos' in extracted_name.lower():
            print("❌ PROBLEM: Still extracting hiring manager name")
        else:
            print("✅ GOOD: Not extracting hiring manager name")
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    # Test with different candidate names
    test_candidates = ['<PERSON> <PERSON>', 'John Doe', 'Anna Mueller', 'Max Mustermann']
    
    print("\n🔍 Test 2: With candidate name validation")
    for candidate_name in test_candidates:
        try:
            result = parser.extract_cv_data_bilingual(cv_file, ['name'], candidate_name)
            extracted_name = result.get('name', 'Not found')
            
            print(f"\n👤 Candidate: {candidate_name}")
            print(f"🔍 Extracted: {extracted_name}")
            
            if extracted_name == candidate_name:
                print("✅ SUCCESS: Used candidate name")
            elif 'eva campos' in extracted_name.lower():
                print("❌ FAILED: Still extracting hiring manager")
            else:
                print("🤔 DIFFERENT: Got different name")
        
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    # Test the actual BAUCH app extractor
    print("\n🔍 Test 3: BAUCH App Extractor")
    try:
        from app import bilingual_extractor
        
        # Test without candidate name
        result_app = bilingual_extractor.extract_cv_data_bilingual(cv_file, ['name'])
        app_name = result_app.get('name', 'Not found')
        print(f"App extractor (no candidate): {app_name}")
        
        # Test with candidate name (this is what will happen in Excel export)
        result_app_with = bilingual_extractor.extract_cv_data_bilingual(cv_file, ['name'], 'Test Candidate')
        app_name_with = result_app_with.get('name', 'Not found')
        print(f"App extractor (with candidate): {app_name_with}")
        
        if app_name_with == 'Test Candidate':
            print("✅ SUCCESS: App extractor using candidate name")
        elif 'eva campos' in app_name_with.lower():
            print("❌ FAILED: App extractor still using hiring manager")
        else:
            print("🤔 DIFFERENT: App extractor got different name")
    
    except Exception as e:
        print(f"❌ ERROR testing app extractor: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Eva Campos Fix Test Completed")

if __name__ == "__main__":
    test_eva_campos_fix()
