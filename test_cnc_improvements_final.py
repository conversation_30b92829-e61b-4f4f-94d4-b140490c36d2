#!/usr/bin/env python3
"""
Final Test of CNC Field Extraction Improvements
Verify that the enhanced hybrid parser correctly extracts CNC-specific fields
"""

import os
from typing import Dict, List
from hybrid_german_cv_parser import HybridGermanCVParser
from hr_database_working import HRDatabase

def test_cnc_improvements():
    """Test the improved CNC field extraction"""
    print("🔧 FINAL CNC FIELD EXTRACTION IMPROVEMENT TEST")
    print("=" * 80)
    
    parser = HybridGermanCVParser()
    
    # Test with CNC-specific samples
    cnc_test_cases = [
        {
            "name": "CNC Programmer",
            "content": """
            <PERSON> Müller
            CNC-Programmierer
            <EMAIL>
            +49 89 123456789
            
            Berufserfahrung:
            2018-2023: CNC-Programmierer bei Maschinenbau GmbH
            - Programmierung von Fanuc und Heidenhain Steuerungen
            - Fräsen und Drehen von Präzisionsteilen
            - Rüsten und Einfahren von CNC-Maschinen
            - Messtechnik und Qualitätskontrolle
            
            Qualifikationen:
            • CNC-Programmierung (Fanuc, Heidenhain)
            • CAD/CAM (SolidWorks, Mastercam)
            • Messtechnik (Mitutoyo, Zeiss)
            • Qualitätsmanagement ISO 9001
            """
        },
        {
            "name": "CNC Operator",
            "content": """
            Anna Schmidt
            CNC-Fräserin
            <EMAIL>
            
            2020-heute: CNC-Fräserin bei Werkzeugbau AG
            - Bedienung von CNC-Fräsmaschinen
            - Rüsten und Einrichten
            - Erstbemusterung und Serienfertigung
            - Koordinatenmesstechnik
            
            Kenntnisse:
            - CNC-Fräsen und Drehen
            - Fanuc Steuerungen
            - Messtechnik
            - Lean Manufacturing
            """
        },
        {
            "name": "Quality Inspector",
            "content": """
            Daniel Meixner
            Qualitätsfachkraft
            <EMAIL>
            
            Erfahrung in:
            • CNC-Messprogrammierung
            • Koordinatenmesstechnik
            • Qualitätskontrolle
            • Erstbemusterung
            • Mitutoyo und Zeiss Messgeräte
            """
        }
    ]
    
    print("📊 TESTING CNC-SPECIFIC EXTRACTION:")
    print("-" * 60)
    
    for i, test_case in enumerate(cnc_test_cases, 1):
        print(f"\n📄 Test Case {i}: {test_case['name']}")
        print("-" * 40)
        
        # Extract using the advanced parser directly
        result = parser.advanced_parser.parse(test_case['content'])
        
        # Map to expected format
        mapped_result = {
            "name": result.get("Name", ""),
            "email": result.get("Email", ""),
            "phone": result.get("Phone", ""),
            "experience": result.get("Berufserfahrung", ""),
            "skills": result.get("Skills", ""),
            "education": result.get("Beruf und Qualifikationen", ""),
        }
        
        print(f"   👤 Name: '{mapped_result['name']}'")
        print(f"   📧 Email: '{mapped_result['email']}'")
        print(f"   📞 Phone: '{mapped_result['phone']}'")
        print(f"   💼 Experience: '{mapped_result['experience']}'")
        print(f"   🛠️  Skills: '{mapped_result['skills']}'")
        
        # Analyze CNC skill detection
        skills = mapped_result['skills'].lower()
        cnc_skills_found = []
        expected_cnc_skills = ['cnc', 'fanuc', 'heidenhain', 'fräsen', 'drehen', 'messtechnik', 'programmierung']
        
        for skill in expected_cnc_skills:
            if skill in skills:
                cnc_skills_found.append(skill)
        
        print(f"   🎯 CNC Skills Found: {cnc_skills_found}")
        print(f"   📊 CNC Detection Rate: {len(cnc_skills_found)}/{len(expected_cnc_skills)} ({(len(cnc_skills_found)/len(expected_cnc_skills))*100:.1f}%)")

def test_real_cnc_cvs():
    """Test with real CNC CVs from database"""
    print("\n🏭 TESTING WITH REAL CNC CVS")
    print("=" * 80)
    
    try:
        db = HRDatabase()
        parser = HybridGermanCVParser()
        
        # Get CNC CVs
        cnc_job = db.get_job_by_title('CNC Fräser')
        if not cnc_job:
            print("❌ CNC job not found")
            return
        
        cvs = db.get_cvs_for_job('CNC Fräser')
        print(f"📄 Testing {len(cvs)} real CNC CVs")
        print()
        
        results = []
        
        for cv in cvs:
            # Find CV file
            cv_file = None
            uploads_dir = "uploads"
            if os.path.exists(uploads_dir):
                for file in os.listdir(uploads_dir):
                    if cv.filename in file or file in cv.filename:
                        cv_file = os.path.join(uploads_dir, file)
                        break
            
            if cv_file:
                result = parser.extract_cv_data(cv_file)
            else:
                # Use content directly
                result = parser.advanced_parser.parse(cv.content)
                result = {
                    "name": result.get("Name", ""),
                    "email": result.get("Email", ""),
                    "phone": result.get("Phone", ""),
                    "experience": result.get("Berufserfahrung", ""),
                    "skills": result.get("Skills", ""),
                    "education": result.get("Beruf und Qualifikationen", ""),
                }
            
            # Count CNC skills
            skills = result.get('skills', '').lower()
            cnc_skills = ['cnc', 'fanuc', 'heidenhain', 'fräsen', 'drehen', 'messtechnik', 'programmierung', 'rüsten']
            found_cnc_skills = [skill for skill in cnc_skills if skill in skills]
            
            results.append({
                'name': cv.candidate_name,
                'cnc_skills_count': len(found_cnc_skills),
                'cnc_skills': found_cnc_skills,
                'total_skills': len(result.get('skills', '').split(',')) if result.get('skills') else 0,
                'has_experience': bool(result.get('experience') and result.get('experience') != 'Berufserfahrung nicht spezifiziert')
            })
        
        # Sort by CNC skills count
        results.sort(key=lambda x: x['cnc_skills_count'], reverse=True)
        
        print("📊 CNC EXTRACTION RESULTS (sorted by CNC skills):")
        print("-" * 60)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['name']}")
            print(f"   🛠️  CNC Skills: {result['cnc_skills_count']} ({result['cnc_skills']})")
            print(f"   📊 Total Skills: {result['total_skills']}")
            print(f"   💼 Has Experience: {'✅' if result['has_experience'] else '❌'}")
            print()
        
        # Calculate averages
        avg_cnc_skills = sum(r['cnc_skills_count'] for r in results) / len(results)
        avg_total_skills = sum(r['total_skills'] for r in results) / len(results)
        
        print(f"📈 SUMMARY:")
        print(f"   Average CNC skills per CV: {avg_cnc_skills:.1f}")
        print(f"   Average total skills per CV: {avg_total_skills:.1f}")
        print(f"   CVs with 3+ CNC skills: {len([r for r in results if r['cnc_skills_count'] >= 3])}/{len(results)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_cnc_skill_vocabulary():
    """Test the CNC skill vocabulary coverage"""
    print("\n📚 TESTING CNC SKILL VOCABULARY")
    print("=" * 80)
    
    from german_cv_parser_advanced import GERMAN_SKILLS
    
    # Check CNC-related skills in vocabulary
    cnc_related = [skill for skill in GERMAN_SKILLS if any(term in skill.lower() for term in 
                   ['cnc', 'fanuc', 'heidenhain', 'fräsen', 'drehen', 'messtechnik', 'rüsten', 'cam', 'cad'])]
    
    print(f"🛠️  CNC-related skills in vocabulary ({len(cnc_related)}):")
    for skill in sorted(cnc_related):
        print(f"   • {skill}")
    
    # Check manufacturing skills
    manufacturing = [skill for skill in GERMAN_SKILLS if any(term in skill.lower() for term in 
                    ['manufacturing', 'machining', 'quality', 'lean', 'iso', 'werkzeug'])]
    
    print(f"\n🏭 Manufacturing skills in vocabulary ({len(manufacturing)}):")
    for skill in sorted(manufacturing):
        print(f"   • {skill}")
    
    print(f"\n📊 VOCABULARY ANALYSIS:")
    print(f"   Total skills in vocabulary: {len(GERMAN_SKILLS)}")
    print(f"   CNC-related skills: {len(cnc_related)} ({(len(cnc_related)/len(GERMAN_SKILLS))*100:.1f}%)")
    print(f"   Manufacturing skills: {len(manufacturing)} ({(len(manufacturing)/len(GERMAN_SKILLS))*100:.1f}%)")

def main():
    """Run comprehensive CNC improvement tests"""
    print("🚀 COMPREHENSIVE CNC FIELD EXTRACTION IMPROVEMENT TEST")
    print("=" * 90)
    
    # Test CNC-specific extraction
    test_cnc_improvements()
    
    # Test with real CVs
    test_real_cnc_cvs()
    
    # Test vocabulary
    test_cnc_skill_vocabulary()
    
    print("\n" + "=" * 90)
    print("✅ CNC IMPROVEMENT TESTING COMPLETED!")
    print("🎯 The hybrid parser should now extract CNC fields much more accurately")
    print("=" * 90)

if __name__ == "__main__":
    main()
