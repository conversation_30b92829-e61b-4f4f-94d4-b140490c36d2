#!/usr/bin/env python3
"""
Test CNC Field Extraction Issues
Analyze why the hybrid parser isn't extracting CNC-specific fields correctly
"""

import os
import glob
from typing import Dict, List
from hybrid_german_cv_parser import HybridGerman<PERSON>VParser
from hr_database_working import HRDatabase

class CNCFieldExtractionTest:
    def __init__(self):
        self.parser = HybridGermanCVParser()
        self.db = HRDatabase()
        
    def test_cnc_specific_extraction(self):
        """Test CNC-specific field extraction"""
        print("🔧 TESTING CNC-SPECIFIC FIELD EXTRACTION")
        print("=" * 70)
        
        # Get CNC job and CVs
        try:
            cnc_job = self.db.get_job_by_title('C<PERSON> Fräser')
            if not cnc_job:
                print("❌ CNC job not found in database")
                return
            
            cvs = self.db.get_cvs_for_job('CNC Fräser')
            print(f"📄 Found {len(cvs)} CVs for CNC job")
            print(f"🎯 Job Description Preview: {cnc_job.description[:200]}...")
            print()
            
            # Test each CV
            for i, cv in enumerate(cvs, 1):
                print(f"📄 {i}. Testing CV: {cv.candidate_name}")
                print("-" * 50)
                
                # Test with file if available
                cv_file_path = self._find_cv_file(cv.filename)
                if cv_file_path:
                    result = self.parser.extract_cv_data(cv_file_path)
                    print(f"   📁 File: {cv.filename}")
                else:
                    # Test with content directly
                    result = self._extract_from_text(cv.content)
                    print(f"   📝 Using CV content directly")
                
                print(f"   👤 Name: '{result.get('name', 'N/A')}'")
                print(f"   📧 Email: '{result.get('email', 'N/A')}'")
                print(f"   📞 Phone: '{result.get('phone', 'N/A')}'")
                print(f"   💼 Experience: '{result.get('experience', 'N/A')}'")
                print(f"   🛠️  Skills: '{result.get('skills', 'N/A')[:100]}{'...' if len(result.get('skills', '')) > 100 else ''}'")
                print(f"   🎓 Education: '{result.get('education', 'N/A')[:100]}{'...' if len(result.get('education', '')) > 100 else ''}'")
                
                # Analyze CNC-specific content
                self._analyze_cnc_content(cv.content, result)
                print()
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    def _find_cv_file(self, filename: str) -> str:
        """Find CV file in uploads directory"""
        uploads_dir = "uploads"
        if not os.path.exists(uploads_dir):
            return None
        
        # Try exact filename
        exact_path = os.path.join(uploads_dir, filename)
        if os.path.exists(exact_path):
            return exact_path
        
        # Try to find similar filename
        for file in os.listdir(uploads_dir):
            if filename in file or file in filename:
                return os.path.join(uploads_dir, file)
        
        return None
    
    def _extract_from_text(self, text: str) -> Dict[str, str]:
        """Extract data directly from text content"""
        # Create a temporary file for testing
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(text)
            temp_file = f.name
        
        try:
            # Use the advanced parser's parse method directly
            result = self.parser.advanced_parser.parse(text)
            
            # Map to expected format
            mapped_result = {
                "name": result.get("Name", ""),
                "email": result.get("Email", ""),
                "phone": result.get("Phone", ""),
                "experience": result.get("Berufserfahrung", ""),
                "skills": result.get("Skills", ""),
                "education": result.get("Beruf und Qualifikationen", ""),
            }
            
            return mapped_result
        finally:
            os.unlink(temp_file)
    
    def _analyze_cnc_content(self, cv_content: str, extracted_result: Dict[str, str]):
        """Analyze CNC-specific content and extraction issues"""
        content_lower = cv_content.lower()
        
        print(f"   🔍 CNC ANALYSIS:")
        
        # Check for CNC-specific terms
        cnc_terms = {
            'cnc': 'cnc' in content_lower,
            'fanuc': 'fanuc' in content_lower,
            'heidenhain': 'heidenhain' in content_lower,
            'fräsen': 'fräsen' in content_lower or 'fraesen' in content_lower,
            'drehen': 'drehen' in content_lower,
            'programmieren': 'programmieren' in content_lower or 'programmierung' in content_lower,
            'rüsten': 'rüsten' in content_lower or 'ruesten' in content_lower,
            'einfahren': 'einfahren' in content_lower,
            'messtechnik': 'messtechnik' in content_lower,
            'qualitätskontrolle': 'qualitätskontrolle' in content_lower or 'qualitaetskontrolle' in content_lower
        }
        
        found_cnc_terms = [term for term, found in cnc_terms.items() if found]
        print(f"      CNC Terms Found: {found_cnc_terms}")
        
        # Check if these terms are in extracted skills
        extracted_skills = extracted_result.get('skills', '').lower()
        skills_captured = [term for term in found_cnc_terms if term in extracted_skills]
        skills_missed = [term for term in found_cnc_terms if term not in extracted_skills]
        
        print(f"      Skills Captured: {skills_captured}")
        print(f"      Skills Missed: {skills_missed}")
        
        # Check experience extraction
        experience = extracted_result.get('experience', '')
        if experience and experience != 'Berufserfahrung nicht spezifiziert':
            print(f"      Experience Quality: ✅ Good")
        else:
            print(f"      Experience Quality: ❌ Poor")
            # Look for experience patterns in content
            import re
            exp_patterns = [
                r'(\d+)\s*jahre?\s*(?:erfahrung|berufserfahrung)',
                r'(\d{4})\s*[-–]\s*(\d{4}|heute)',
                r'seit\s*(\d{4})',
                r'(\d+)\s*jahre?\s*(?:als|im|in)'
            ]
            
            for pattern in exp_patterns:
                matches = re.findall(pattern, content_lower)
                if matches:
                    print(f"         Found pattern: {pattern} -> {matches}")
        
        # Check for specific CNC job titles
        cnc_job_titles = ['cnc-fräser', 'cnc fräser', 'cnc-dreher', 'cnc dreher', 'cnc-programmierer', 'cnc programmierer']
        found_titles = [title for title in cnc_job_titles if title in content_lower]
        print(f"      CNC Job Titles: {found_titles}")
    
    def test_cnc_skills_detection(self):
        """Test CNC skills detection specifically"""
        print("🛠️ TESTING CNC SKILLS DETECTION")
        print("=" * 70)
        
        # Test with CNC-specific text samples
        cnc_samples = [
            """
            CNC-Fräser mit 5 Jahren Erfahrung
            Fanuc und Heidenhain Steuerungen
            Programmierung von CNC-Maschinen
            Rüsten und Einfahren
            Messtechnik und Qualitätskontrolle
            """,
            """
            Berufserfahrung:
            2018-2023: CNC-Programmierer bei Maschinenbau GmbH
            - Programmierung von Fanuc-Steuerungen
            - Fräsen und Drehen
            - Qualitätsprüfung
            """,
            """
            Qualifikationen:
            • CNC-Programmierung (Fanuc, Heidenhain)
            • Messtechnik
            • Rüsten von CNC-Maschinen
            • CAD/CAM
            """
        ]
        
        for i, sample in enumerate(cnc_samples, 1):
            print(f"📝 Sample {i}:")
            print("-" * 30)
            
            result = self._extract_from_text(sample)
            
            print(f"   Skills: '{result.get('skills', 'N/A')}'")
            print(f"   Experience: '{result.get('experience', 'N/A')}'")
            
            # Check what should have been detected
            sample_lower = sample.lower()
            expected_skills = []
            if 'cnc' in sample_lower:
                expected_skills.append('CNC')
            if 'fanuc' in sample_lower:
                expected_skills.append('Fanuc')
            if 'heidenhain' in sample_lower:
                expected_skills.append('Heidenhain')
            if 'programmier' in sample_lower:
                expected_skills.append('Programmierung')
            if 'messtechnik' in sample_lower:
                expected_skills.append('Messtechnik')
            
            print(f"   Expected: {expected_skills}")
            print()
    
    def identify_extraction_issues(self):
        """Identify specific issues with CNC field extraction"""
        print("🔍 IDENTIFYING CNC EXTRACTION ISSUES")
        print("=" * 70)
        
        issues = []
        
        # Check if CNC skills are in the skill vocabulary
        from german_cv_parser_advanced import GERMAN_SKILLS
        
        cnc_skills_in_vocab = [skill for skill in GERMAN_SKILLS if 'cnc' in skill.lower()]
        print(f"CNC skills in vocabulary: {cnc_skills_in_vocab}")
        
        if not cnc_skills_in_vocab:
            issues.append("❌ No CNC-specific skills in German skill vocabulary")
        
        # Check for manufacturing skills
        manufacturing_skills = [skill for skill in GERMAN_SKILLS if any(term in skill.lower() for term in ['manufacturing', 'machining', 'fanuc', 'heidenhain'])]
        print(f"Manufacturing skills in vocabulary: {manufacturing_skills}")
        
        if len(manufacturing_skills) < 5:
            issues.append("❌ Limited manufacturing skills in vocabulary")
        
        # Check German manufacturing terms
        german_manufacturing = [skill for skill in GERMAN_SKILLS if any(term in skill.lower() for term in ['fräsen', 'drehen', 'rüsten', 'messtechnik'])]
        print(f"German manufacturing terms: {german_manufacturing}")
        
        if not german_manufacturing:
            issues.append("❌ Missing German manufacturing terms")
        
        print(f"\n🚨 IDENTIFIED ISSUES:")
        for issue in issues:
            print(f"   {issue}")
        
        if not issues:
            print("   ✅ No obvious vocabulary issues found")
        
        return issues
    
    def run_comprehensive_cnc_test(self):
        """Run comprehensive CNC field extraction test"""
        print("🚀 COMPREHENSIVE CNC FIELD EXTRACTION TEST")
        print("=" * 80)
        
        # Test CNC-specific extraction
        self.test_cnc_specific_extraction()
        print("\n" + "="*80 + "\n")
        
        # Test CNC skills detection
        self.test_cnc_skills_detection()
        print("\n" + "="*80 + "\n")
        
        # Identify issues
        issues = self.identify_extraction_issues()
        
        print(f"\n✅ CNC FIELD EXTRACTION TEST COMPLETED!")
        if issues:
            print("⚠️  Issues found that may affect CNC extraction quality")
        else:
            print("✅ No major issues identified")
        print("=" * 80)

def main():
    """Run CNC field extraction tests"""
    test_suite = CNCFieldExtractionTest()
    test_suite.run_comprehensive_cnc_test()

if __name__ == "__main__":
    main()
