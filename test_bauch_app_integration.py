#!/usr/bin/env python3
"""
BAUCH Application Integration Test
=================================
Tests the perfect CV parser integration with the actual BAUCH application components.

This test validates:
1. Integration with the main app.py
2. Compatibility with existing database structures
3. Excel export functionality
4. CV matching functionality
5. Real-world performance with uploaded CVs
"""

import os
import sys
import glob
from typing import Dict, List

# Test imports from BAUCH application
try:
    from app import bilingual_extractor, hr_db
    APP_AVAILABLE = True
    print("✅ BAUCH app components loaded successfully")
except ImportError as e:
    print(f"❌ BAUCH app components not available: {e}")
    APP_AVAILABLE = False

try:
    from hybrid_german_cv_parser import HybridGermanCVParser
    HYBRID_AVAILABLE = True
except ImportError:
    HYBRID_AVAILABLE = False

class BAUCHAppIntegrationTester:
    """Test integration with BAUCH application"""
    
    def __init__(self):
        """Initialize the tester"""
        self.test_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']
        
        if APP_AVAILABLE:
            self.app_extractor = bilingual_extractor
            self.database = hr_db
        else:
            self.app_extractor = None
            self.database = None
    
    def run_integration_tests(self):
        """Run comprehensive integration tests"""
        print("\n" + "="*80)
        print("🔗 BAUCH APPLICATION INTEGRATION TEST SUITE")
        print("="*80)
        
        if not APP_AVAILABLE:
            print("❌ BAUCH application not available for testing")
            return
        
        # Test 1: Extractor compatibility
        print("\n📋 Test 1: Extractor Compatibility")
        self.test_extractor_compatibility()
        
        # Test 2: Real CV processing
        print("\n📋 Test 2: Real CV Processing")
        self.test_real_cv_processing()
        
        # Test 3: Database integration
        print("\n📋 Test 3: Database Integration")
        self.test_database_integration()
        
        # Test 4: Performance comparison
        print("\n📋 Test 4: Performance Comparison")
        self.test_performance_comparison()
        
        print("\n" + "="*80)
        print("✅ BAUCH INTEGRATION TESTS COMPLETED")
        print("="*80)
    
    def test_extractor_compatibility(self):
        """Test compatibility with BAUCH's extractor interface"""
        print("Testing extractor compatibility...")
        
        # Check if the app extractor has required methods
        required_methods = ['extract_cv_data', 'extract_cv_data_bilingual', 'get_parser_info']
        
        for method in required_methods:
            if hasattr(self.app_extractor, method):
                print(f"  ✅ {method} method available")
            else:
                print(f"  ❌ {method} method missing")
        
        # Test parser info
        if hasattr(self.app_extractor, 'get_parser_info'):
            try:
                info = self.app_extractor.get_parser_info()
                print(f"  ℹ️  Parser: {info.get('name', 'Unknown')}")
                print(f"  ℹ️  Version: {info.get('version', 'Unknown')}")
                print(f"  ℹ️  Primary Engine: {info.get('primary_engine', 'Unknown')}")
                print(f"  ℹ️  Perfect Parser: {info.get('perfect_parser_available', 'Unknown')}")
            except Exception as e:
                print(f"  ❌ Error getting parser info: {e}")
    
    def test_real_cv_processing(self):
        """Test processing real CV files from uploads"""
        print("Testing real CV processing...")
        
        # Find CV files
        upload_dir = "uploads"
        cv_files = []
        for ext in ['*.pdf', '*.docx']:
            cv_files.extend(glob.glob(os.path.join(upload_dir, ext)))
        
        if not cv_files:
            print("  ❌ No CV files found for testing")
            return
        
        print(f"  📁 Found {len(cv_files)} CV files")
        
        # Test with first 3 files
        test_files = cv_files[:3]
        successful_extractions = 0
        
        for file_path in test_files:
            filename = os.path.basename(file_path)
            print(f"\n  📄 Testing: {filename}")
            
            try:
                # Test bilingual extraction (what BAUCH uses)
                result = self.app_extractor.extract_cv_data_bilingual(
                    file_path, self.test_fields
                )
                
                if 'error' in result:
                    print(f"    ❌ Error: {result['error']}")
                else:
                    successful_extractions += 1
                    extracted_fields = sum(1 for v in result.values() if v and v.strip())
                    print(f"    ✅ Success: {extracted_fields}/{len(self.test_fields)} fields")
                    
                    # Show key extracted data
                    if result.get('name'):
                        print(f"      👤 Name: {result['name'][:30]}...")
                    if result.get('email'):
                        print(f"      📧 Email: {result['email']}")
                    if result.get('skills'):
                        skills = result['skills'][:50] + "..." if len(result['skills']) > 50 else result['skills']
                        print(f"      🛠️  Skills: {skills}")
            
            except Exception as e:
                print(f"    ❌ Exception: {str(e)[:50]}...")
        
        success_rate = (successful_extractions / len(test_files)) * 100
        print(f"\n  📊 Success Rate: {success_rate:.1f}% ({successful_extractions}/{len(test_files)})")
    
    def test_database_integration(self):
        """Test integration with BAUCH database"""
        print("Testing database integration...")
        
        if not self.database:
            print("  ❌ Database not available")
            return
        
        try:
            # Test getting jobs (should work with existing database)
            jobs = self.database.get_all_jobs()
            print(f"  ✅ Database connection works: {len(jobs)} jobs found")
            
            if jobs:
                # Test getting CVs for first job
                first_job = jobs[0]
                cvs = self.database.get_cvs_for_job(first_job.title)
                print(f"  ✅ CV retrieval works: {len(cvs)} CVs for '{first_job.title}'")
                
                if cvs:
                    # Test extracting data from first CV
                    first_cv = cvs[0]
                    print(f"  📄 Testing extraction for: {first_cv.filename}")
                    
                    # Find the actual file
                    upload_dir = "uploads"
                    possible_files = [
                        os.path.join(upload_dir, first_cv.filename),
                        os.path.join(upload_dir, first_cv.filename.split('_', 1)[-1])
                    ]
                    
                    cv_file = None
                    for file_path in possible_files:
                        if os.path.exists(file_path):
                            cv_file = file_path
                            break
                    
                    if cv_file:
                        result = self.app_extractor.extract_cv_data_bilingual(
                            cv_file, self.test_fields
                        )
                        
                        if 'error' not in result:
                            print(f"    ✅ Extraction successful for database CV")
                        else:
                            print(f"    ❌ Extraction failed: {result['error']}")
                    else:
                        print(f"    ⚠️  CV file not found in uploads")
        
        except Exception as e:
            print(f"  ❌ Database integration error: {str(e)[:50]}...")
    
    def test_performance_comparison(self):
        """Compare performance with and without perfect parser"""
        print("Testing performance comparison...")
        
        # Find a test file
        upload_dir = "uploads"
        cv_files = glob.glob(os.path.join(upload_dir, "*.pdf"))
        
        if not cv_files:
            print("  ❌ No PDF files for performance testing")
            return
        
        test_file = cv_files[0]
        filename = os.path.basename(test_file)
        print(f"  📄 Performance test with: {filename}")
        
        # Test current app extractor (with perfect parser)
        try:
            import time
            
            start_time = time.time()
            result_perfect = self.app_extractor.extract_cv_data_bilingual(
                test_file, self.test_fields
            )
            perfect_time = time.time() - start_time
            
            perfect_fields = sum(1 for v in result_perfect.values() if v and v.strip() and 'nicht spezifiziert' not in v)
            
            print(f"  ⚡ Perfect parser: {perfect_time:.2f}s, {perfect_fields} fields")
            
            # Test fallback parser if available
            if HYBRID_AVAILABLE:
                fallback_parser = HybridGermanCVParser()
                # Temporarily disable perfect parser for comparison
                fallback_parser.perfect_available = False
                
                start_time = time.time()
                result_fallback = fallback_parser.extract_cv_data(test_file, self.test_fields)
                fallback_time = time.time() - start_time
                
                fallback_fields = sum(1 for v in result_fallback.values() if v and v.strip() and 'nicht spezifiziert' not in v)
                
                print(f"  🔄 Fallback parser: {fallback_time:.2f}s, {fallback_fields} fields")
                
                # Compare results
                improvement = perfect_fields - fallback_fields
                if improvement > 0:
                    print(f"  📈 Perfect parser extracted {improvement} more fields")
                elif improvement < 0:
                    print(f"  📉 Perfect parser extracted {abs(improvement)} fewer fields")
                else:
                    print(f"  ➡️  Same number of fields extracted")
        
        except Exception as e:
            print(f"  ❌ Performance test error: {str(e)[:50]}...")


def main():
    """Main test function"""
    print("🚀 Starting BAUCH Application Integration Tests...")
    
    tester = BAUCHAppIntegrationTester()
    tester.run_integration_tests()


if __name__ == "__main__":
    main()
