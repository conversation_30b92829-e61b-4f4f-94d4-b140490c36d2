#!/usr/bin/env python3
"""
German CV Field Extraction Accuracy Test
========================================
Focused testing of German CV field extraction accuracy with the new perfect parser.

This test creates realistic German CV content and validates extraction accuracy
for all fields that BAUCH requires.
"""

import os
import tempfile
from typing import Dict, List
from pathlib import Path

# Import the extractors
try:
    from german_cv_parser_perfect import GermanCVParserPerfect
    PERFECT_AVAILABLE = True
except ImportError:
    PERFECT_AVAILABLE = False

try:
    from bauch_cv_extractor_perfect import BAUCHCVExtractorPerfect
    BAUCH_PERFECT_AVAILABLE = True
except ImportError:
    BAUCH_PERFECT_AVAILABLE = False

try:
    from hybrid_german_cv_parser import HybridGermanCVParser
    HYBRID_AVAILABLE = True
except ImportError:
    HYBRID_AVAILABLE = False

class GermanCVAccuracyTester:
    """Test German CV extraction accuracy"""
    
    def __init__(self):
        """Initialize extractors"""
        self.extractors = {}
        
        if PERFECT_AVAILABLE:
            self.extractors['perfect'] = GermanCVParserPerfect()
        
        if BAUCH_PERFECT_AVAILABLE:
            self.extractors['bauch_perfect'] = BAUCHCVExtractorPerfect()
        
        if HYBRID_AVAILABLE:
            self.extractors['hybrid'] = HybridGermanCVParser()
        
        # Test cases with expected results
        self.test_cases = self._create_test_cases()
    
    def _create_test_cases(self) -> List[Dict]:
        """Create comprehensive German CV test cases"""
        return [
            {
                "name": "CNC Machinist CV",
                "content": """
                Hans Müller
                CNC-Zerspanungsmechaniker
                
                Persönliche Daten:
                Geburtsdatum: 15.03.1985
                Staatsangehörigkeit: Deutsch
                Familienstand: Verheiratet
                Straße: Hauptstraße 123
                PLZ: 12345 Berlin
                Email: <EMAIL>
                Telefon: +49 30 12345678
                
                Berufserfahrung:
                2020-heute CNC-Programmierer bei Bauch GmbH & Co. KG
                2018-2020 Maschinenbediener bei TechCorp AG
                2015-2018 Zerspanungsmechaniker bei MetallWorks GmbH
                
                Qualifikationen:
                - Ausbildung zum Zerspanungsmechaniker
                - Weiterbildung CNC-Programmierung
                - Meisterprüfung Maschinenbau
                
                Fähigkeiten:
                CNC-Programmierung, SolidWorks, Fanuc, Siemens, Heidenhain, 
                Qualitätssicherung, Messtechnik, CAD, Maschinenbau
                
                Sprachen: Deutsch (Muttersprache), Englisch (Grundkenntnisse)
                Kündigungsfrist: 3 Monate zum Monatsende
                """,
                "expected": {
                    "name": "Hans Müller",
                    "email": "<EMAIL>",
                    "phone": "+49 30 12345678",
                    "experience": "9 Jahre Berufserfahrung",  # 2015-2024
                    "skills": "cnc, solidworks, fanuc, siemens, heidenhain, qualitätssicherung, messtechnik",
                    "education": "ausbildung, meister"
                }
            },
            {
                "name": "Software Engineer CV",
                "content": """
                Dr. Anna Schmidt
                Software-Ingenieurin
                
                Kontakt:
                <EMAIL>
                0171 9876543
                Musterweg 45, 80331 München
                
                Geboren: 22.07.1990 in Hamburg
                Nationalität: Deutsche
                
                Beruflicher Werdegang:
                2022-aktuell Senior Software Engineer bei TechStart GmbH
                2019-2022 Software Developer bei DataCorp
                2017-2019 Junior Entwickler bei WebSolutions
                
                Ausbildung:
                2013-2017 Studium Informatik, TU München (Master of Science)
                2010-2013 Bachelor Informatik, Universität Hamburg
                
                Technische Fähigkeiten:
                Python, Java, JavaScript, SQL, Docker, Kubernetes, AWS, 
                React, Git, CI/CD, Machine Learning, Pandas, NumPy
                
                Sprachen: Deutsch, Englisch (fließend), Französisch (Grundlagen)
                """,
                "expected": {
                    "name": "Anna Schmidt",
                    "email": "<EMAIL>", 
                    "phone": "0171 9876543",
                    "experience": "7 Jahre Berufserfahrung",  # 2017-2024
                    "skills": "python, java, javascript, sql, docker, kubernetes",
                    "education": "master, bachelor, studium, universität"
                }
            },
            {
                "name": "Manufacturing Technician CV",
                "content": """
                Michael Weber
                Produktionstechniker
                
                Anschrift: Industriestraße 78, 45127 Essen
                Telefon: 0201/555-1234
                E-Mail: <EMAIL>
                Geburtsdatum: 10.11.1982
                
                Berufserfahrung:
                2018-heute Produktionsleiter bei ManufacturingPlus GmbH
                2015-2018 Schichtleiter bei IndustrieWerk AG
                2012-2015 Maschinenbediener bei FabrikTech
                2010-2012 Produktionshelfer bei QuickProd
                
                Ausbildung:
                2008-2010 Techniker für Maschinenbau, Berufsschule Essen
                2005-2008 Ausbildung Industriemechaniker
                
                Kompetenzen:
                Lean Manufacturing, SAP, Qualitätssicherung, Wartung, 
                Instandhaltung, Schweißen, Montage, Teamleitung
                
                Kündigungsfrist: 6 Wochen zum Quartalsende
                """,
                "expected": {
                    "name": "Michael Weber",
                    "email": "<EMAIL>",
                    "phone": "0201/555-1234", 
                    "experience": "14 Jahre Berufserfahrung",  # 2010-2024
                    "skills": "lean manufacturing, sap, qualitätssicherung, wartung, instandhaltung, schweißen, montage",
                    "education": "techniker, ausbildung"
                }
            }
        ]
    
    def run_accuracy_tests(self):
        """Run comprehensive accuracy tests"""
        print("\n" + "="*80)
        print("🎯 GERMAN CV FIELD EXTRACTION ACCURACY TEST")
        print("="*80)
        
        if not self.extractors:
            print("❌ No extractors available for testing!")
            return
        
        total_tests = 0
        total_correct = 0
        results_by_extractor = {}
        
        for extractor_name in self.extractors.keys():
            results_by_extractor[extractor_name] = {"correct": 0, "total": 0}
        
        # Test each case
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"\n📋 Test Case {i}: {test_case['name']}")
            print("-" * 50)
            
            # Create temporary file with test content
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(test_case['content'])
                temp_file = f.name
            
            try:
                # Test each extractor
                for extractor_name, extractor in self.extractors.items():
                    print(f"\n🔍 Testing {extractor_name}:")
                    
                    try:
                        # Extract data
                        if hasattr(extractor, 'extract_cv_data'):
                            result = extractor.extract_cv_data(temp_file, ['name', 'email', 'phone', 'experience', 'skills', 'education'])
                        elif hasattr(extractor, 'parse_text'):
                            result = extractor.parse_text(test_case['content'])
                            # Map perfect parser results to BAUCH format
                            result = self._map_perfect_results(result)
                        else:
                            print("  ❌ No suitable extraction method")
                            continue
                        
                        if 'error' in result:
                            print(f"  ❌ Error: {result['error']}")
                            continue
                        
                        # Check accuracy for each field
                        field_results = {}
                        for field, expected in test_case['expected'].items():
                            extracted = result.get(field, '').lower().strip()
                            expected_lower = expected.lower().strip()
                            
                            # Different validation strategies per field
                            if field == 'name':
                                correct = self._validate_name(extracted, expected_lower)
                            elif field == 'email':
                                correct = extracted == expected_lower
                            elif field == 'phone':
                                correct = self._validate_phone(extracted, expected_lower)
                            elif field == 'experience':
                                correct = self._validate_experience(extracted, expected_lower)
                            elif field == 'skills':
                                correct = self._validate_skills(extracted, expected_lower)
                            elif field == 'education':
                                correct = self._validate_education(extracted, expected_lower)
                            else:
                                correct = expected_lower in extracted
                            
                            field_results[field] = correct
                            results_by_extractor[extractor_name]["total"] += 1
                            total_tests += 1
                            
                            if correct:
                                results_by_extractor[extractor_name]["correct"] += 1
                                total_correct += 1
                            
                            status = "✅" if correct else "❌"
                            print(f"    {status} {field}: {extracted[:40]}...")
                        
                        # Calculate accuracy for this test case
                        correct_count = sum(field_results.values())
                        accuracy = (correct_count / len(field_results)) * 100
                        print(f"  📊 Accuracy: {accuracy:.1f}% ({correct_count}/{len(field_results)})")
                    
                    except Exception as e:
                        print(f"  ❌ Exception: {str(e)[:50]}...")
            
            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_file)
                except:
                    pass
        
        # Print overall results
        print("\n" + "="*80)
        print("📊 OVERALL ACCURACY RESULTS")
        print("="*80)
        
        for extractor_name, stats in results_by_extractor.items():
            if stats["total"] > 0:
                accuracy = (stats["correct"] / stats["total"]) * 100
                print(f"{extractor_name:20}: {accuracy:5.1f}% ({stats['correct']}/{stats['total']})")
        
        if total_tests > 0:
            overall_accuracy = (total_correct / total_tests) * 100
            print(f"{'OVERALL':20}: {overall_accuracy:5.1f}% ({total_correct}/{total_tests})")
    
    def _map_perfect_results(self, perfect_result: Dict) -> Dict:
        """Map perfect parser results to BAUCH format"""
        mapped = {}
        
        # Map name
        first_name = perfect_result.get("Vorname", "").strip()
        last_name = perfect_result.get("Name", "").strip()
        if first_name and last_name:
            mapped['name'] = f"{first_name} {last_name}"
        elif first_name or last_name:
            mapped['name'] = first_name or last_name
        else:
            mapped['name'] = ""
        
        # Direct mappings
        mapped['email'] = perfect_result.get("Email", "")
        mapped['phone'] = perfect_result.get("Phone", "")
        mapped['experience'] = perfect_result.get("Berufserfahrung", "")
        mapped['skills'] = perfect_result.get("Skills", "")
        
        # Education from qualifications
        qualifications = perfect_result.get("Beruf und Qualifikationen", "")
        mapped['education'] = qualifications
        
        return mapped
    
    def _validate_name(self, extracted: str, expected: str) -> bool:
        """Validate name extraction"""
        if not extracted:
            return False
        # Check if main parts of the name are present
        expected_parts = expected.split()
        return all(part in extracted for part in expected_parts)
    
    def _validate_phone(self, extracted: str, expected: str) -> bool:
        """Validate phone extraction"""
        if not extracted:
            return False
        # Remove all non-digit characters for comparison
        extracted_digits = ''.join(c for c in extracted if c.isdigit())
        expected_digits = ''.join(c for c in expected if c.isdigit())
        return extracted_digits == expected_digits
    
    def _validate_experience(self, extracted: str, expected: str) -> bool:
        """Validate experience extraction"""
        if not extracted:
            return False
        # Look for years in the extracted text
        import re
        years_match = re.search(r'(\d+)', extracted)
        expected_match = re.search(r'(\d+)', expected)
        if years_match and expected_match:
            return abs(int(years_match.group(1)) - int(expected_match.group(1))) <= 1
        return False
    
    def _validate_skills(self, extracted: str, expected: str) -> bool:
        """Validate skills extraction"""
        if not extracted:
            return False
        expected_skills = [s.strip() for s in expected.split(',')]
        extracted_lower = extracted.lower()
        # Check if at least 50% of expected skills are found
        found_skills = sum(1 for skill in expected_skills if skill in extracted_lower)
        return found_skills >= len(expected_skills) * 0.5
    
    def _validate_education(self, extracted: str, expected: str) -> bool:
        """Validate education extraction"""
        if not extracted:
            return False
        expected_terms = [s.strip() for s in expected.split(',')]
        extracted_lower = extracted.lower()
        # Check if at least one expected term is found
        return any(term in extracted_lower for term in expected_terms)


def main():
    """Main test function"""
    print("🎯 Starting German CV Field Extraction Accuracy Tests...")
    
    tester = GermanCVAccuracyTester()
    tester.run_accuracy_tests()


if __name__ == "__main__":
    main()
