#!/usr/bin/env python3
"""
Test CNC CV Extraction Fix
==========================
Verify that the CNC CV extraction is now working correctly with proper skills.
"""

import glob
from app import bilingual_extractor

def test_cnc_extraction():
    """Test CNC CV extraction with the fixed parser"""
    print("🔧 Testing CNC CV Extraction Fix")
    print("=" * 50)
    
    # Get extractor info
    info = bilingual_extractor.get_parser_info()
    print(f"Extractor: {info['name']}")
    print(f"Perfect Parser Active: {info.get('perfect_parser_available', 'Unknown')}")
    print(f"Primary Engine: {info.get('primary_engine', 'Unknown')}")
    
    # Test with CNC CVs
    cv_files = glob.glob('uploads/*.pdf')
    cnc_related_files = [f for f in cv_files if any(keyword in f.lower() for keyword in ['daniel', 'meixner', 'cnc', 'frank', 'reichelt'])]
    
    if not cnc_related_files:
        cnc_related_files = cv_files[:3]  # Use first 3 files if no CNC-specific files found
    
    print(f"\n📁 Testing {len(cnc_related_files)} CV files:")
    
    for cv_file in cnc_related_files:
        filename = cv_file.split('\\')[-1]
        print(f"\n📄 {filename}")
        print("-" * 30)
        
        try:
            result = bilingual_extractor.extract_cv_data_bilingual(
                cv_file, ['name', 'email', 'phone', 'skills', 'experience']
            )
            
            if 'error' in result:
                print(f"❌ Error: {result['error']}")
                continue
            
            # Display results
            print(f"👤 Name: {result.get('name', 'Not found')}")
            print(f"📧 Email: {result.get('email', 'Not found')}")
            print(f"📞 Phone: {result.get('phone', 'Not found')}")
            print(f"💼 Experience: {result.get('experience', 'Not found')}")
            
            skills = result.get('skills', 'Not found')
            print(f"🛠️  Skills: {skills}")
            
            # Check if skills are CNC-related (not programming languages)
            if skills and skills != 'Not found':
                cnc_skills = ['cnc', 'drehen', 'fräsen', 'maschinenbau', 'qualität', 'messtechnik', 'montage']
                programming_skills = ['go', 'r', 'python', 'java', 'javascript']
                
                has_cnc_skills = any(skill in skills.lower() for skill in cnc_skills)
                has_programming_skills = any(skill in skills.lower() for skill in programming_skills)
                
                if has_cnc_skills and not has_programming_skills:
                    print("✅ Skills look correct for CNC job")
                elif has_programming_skills and not has_cnc_skills:
                    print("❌ Skills show programming languages instead of CNC skills")
                elif has_cnc_skills and has_programming_skills:
                    print("⚠️  Skills mix CNC and programming (might be correct)")
                else:
                    print("ℹ️  Skills don't match common CNC or programming patterns")
        
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
    
    print("\n" + "=" * 50)
    print("✅ CNC CV Extraction Test Completed")

if __name__ == "__main__":
    test_cnc_extraction()
