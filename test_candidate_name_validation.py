#!/usr/bin/env python3
"""
Test Candidate Name Validation
==============================
Test the improved name extraction that validates against the candidate name from upload.
"""

import glob
from hybrid_german_cv_parser import HybridGermanCVParser

def test_candidate_name_validation():
    """Test candidate name validation functionality"""
    print("🔧 Testing Candidate Name Validation")
    print("=" * 60)
    
    parser = HybridGermanCVParser()
    
    # Test cases with known candidate names
    test_cases = [
        {
            'search': 'daniel',
            'candidate_name': '<PERSON>',
            'description': '<PERSON> (should not extract <PERSON>)'
        },
        {
            'search': 'werner',
            'candidate_name': '<PERSON>', 
            'description': '<PERSON> (should not extract technical terms)'
        },
        {
            'search': 'frank',
            'candidate_name': '<PERSON>',
            'description': '<PERSON>'
        },
        {
            'search': 'eva',
            'candidate_name': 'Eva Test',  # This should be the applicant, not hiring manager
            'description': '<PERSON> CV (should use candidate name, not hiring manager)'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📄 {test_case['description']}")
        print("-" * 40)
        
        # Find the CV file
        cv_files = [f for f in glob.glob('uploads/*.pdf') if test_case['search'] in f.lower()]
        
        if not cv_files:
            print(f"❌ No CV found for: {test_case['search']}")
            continue
        
        cv_file = cv_files[0]
        filename = cv_file.split('\\')[-1]
        candidate_name = test_case['candidate_name']
        
        try:
            # Test WITHOUT candidate name (old behavior)
            result_without = parser.extract_cv_data(cv_file, ['name'])
            extracted_without = result_without.get('name', 'Not found')
            
            # Test WITH candidate name (new behavior)
            result_with = parser.extract_cv_data(cv_file, ['name'], candidate_name)
            extracted_with = result_with.get('name', 'Not found')
            
            print(f"📁 File: {filename}")
            print(f"👤 Candidate Name: {candidate_name}")
            print(f"🔍 Without validation: {extracted_without}")
            print(f"✅ With validation: {extracted_with}")
            
            # Check if validation improved the result
            if extracted_with == candidate_name:
                print(f"✅ SUCCESS: Used candidate name")
            elif extracted_without != extracted_with:
                print(f"🔄 IMPROVED: Changed from '{extracted_without}' to '{extracted_with}'")
            else:
                print(f"➡️  UNCHANGED: Extraction remained the same")
            
            # Check for problematic patterns
            problematic_words = ['eva campos', 'technische', 'leitung', 'telefon', 'mainburg']
            has_problematic = any(word in extracted_with.lower() for word in problematic_words)
            
            if has_problematic:
                print(f"❌ STILL PROBLEMATIC: Contains hiring manager/job title words")
            else:
                print(f"✅ CLEAN: No problematic words detected")
        
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
    
    # Test with bilingual method (what BAUCH actually uses)
    print(f"\n" + "=" * 60)
    print("🌐 Testing Bilingual Method (BAUCH App)")
    print("=" * 60)
    
    # Test the Eva Campos case specifically
    eva_files = [f for f in glob.glob('uploads/*.pdf') if 'bewerbung.pdf' in f.lower()]
    if eva_files:
        cv_file = eva_files[0]
        filename = cv_file.split('\\')[-1]
        
        print(f"\n📄 Testing: {filename}")
        print("This CV likely contains 'Eva Campos' (hiring manager name)")
        
        # Test different candidate names
        test_candidates = ['Maria Schmidt', 'John Doe', 'Anna Mueller']
        
        for candidate_name in test_candidates:
            try:
                result = parser.extract_cv_data_bilingual(cv_file, ['name'], candidate_name)
                extracted_name = result.get('name', 'Not found')
                
                print(f"\n👤 Candidate: {candidate_name}")
                print(f"🔍 Extracted: {extracted_name}")
                
                if extracted_name == candidate_name:
                    print(f"✅ SUCCESS: Used candidate name instead of hiring manager")
                elif 'eva campos' in extracted_name.lower():
                    print(f"❌ FAILED: Still extracting hiring manager name")
                else:
                    print(f"🤔 DIFFERENT: Got different name")
            
            except Exception as e:
                print(f"❌ ERROR: {str(e)}")
    
    print(f"\n" + "=" * 60)
    print("✅ Candidate Name Validation Test Completed")
    print("=" * 60)

if __name__ == "__main__":
    test_candidate_name_validation()
