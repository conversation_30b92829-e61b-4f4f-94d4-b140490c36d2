#!/usr/bin/env python3
"""
Test Excel Export Simulation
============================
Simulate the exact Excel export process to see what's happening.
"""

import os
import glob
from app import bilingual_extractor, hr_db

def test_excel_export_simulation():
    """Simulate the Excel export process"""
    print("🔧 Testing Excel Export Simulation")
    print("=" * 60)
    
    # Get the extractor being used
    extractor = bilingual_extractor
    print(f"Extractor type: {type(extractor).__name__}")
    
    try:
        info = extractor.get_parser_info()
        print(f"Extractor name: {info['name']}")
        print(f"Perfect parser available: {info.get('perfect_parser_available', 'Unknown')}")
    except:
        print("Could not get extractor info")
    
    # Get CNC jobs
    jobs = hr_db.get_all_jobs()
    cnc_jobs = [job for job in jobs if 'cnc' in job.title.lower()]
    
    if not cnc_jobs:
        print("❌ No CNC jobs found")
        return
    
    cnc_job = cnc_jobs[0]
    print(f"\n📋 CNC Job: {cnc_job.title}")
    
    # Get CVs for the job
    cvs = hr_db.get_cvs_for_job(cnc_job.title)
    print(f"📁 CVs found: {len(cvs)}")
    
    if not cvs:
        print("❌ No CVs found for CNC job")
        return
    
    # Test extraction for each CV (simulate Excel export)
    fields = ['name', 'email', 'phone', 'skills']
    upload_dir = 'uploads'
    
    print(f"\n🔍 Simulating Excel Export Process:")
    print("-" * 40)
    
    for i, cv in enumerate(cvs[:5]):  # Test first 5 CVs
        print(f"\n📄 CV {i+1}: {cv.filename}")
        print(f"👤 Candidate name in DB: {cv.candidate_name}")
        
        # Find the CV file (same logic as Excel export)
        cv_path = os.path.join(upload_dir, cv.filename)
        if not os.path.exists(cv_path):
            # Try alternative filename
            for file in os.listdir(upload_dir):
                if cv.filename.endswith(file) or file.endswith(cv.filename.split('_')[-1]):
                    cv_path = os.path.join(upload_dir, file)
                    break
        
        if cv_path and os.path.exists(cv_path):
            print(f"📁 File found: {os.path.basename(cv_path)}")
            
            try:
                # Simulate the exact Excel export extraction
                if hasattr(extractor, 'extract_cv_data_bilingual'):
                    print("🔄 Using extract_cv_data_bilingual method")
                    extracted_data = extractor.extract_cv_data_bilingual(cv_path, fields, cv.candidate_name)
                else:
                    print("🔄 Using extract_cv_data method")
                    extracted_data = extractor.extract_cv_data(cv_path, fields, cv.candidate_name)
                
                # Show what would go in Excel
                name = extracted_data.get('name', 'N/A')
                skills = extracted_data.get('skills', 'N/A')
                
                print(f"📊 Excel would show:")
                print(f"   Name: {name}")
                print(f"   Skills: {skills[:50]}..." if len(skills) > 50 else f"   Skills: {skills}")
                
                # Check for problems
                if name == cv.candidate_name:
                    print("✅ GOOD: Using candidate name from DB")
                elif 'eva campos' in name.lower():
                    print("❌ PROBLEM: Still showing hiring manager")
                elif any(word in name.lower() for word in ['technische', 'leitung', 'telefon', 'mainburg']):
                    print("❌ PROBLEM: Still showing job title/location")
                else:
                    print("🤔 DIFFERENT: Name doesn't match candidate name")
                
                # Check skills
                if 'go' in skills.lower() and 'r' in skills.lower() and len(skills) < 10:
                    print("❌ PROBLEM: Still showing programming languages (Go, R)")
                elif any(cnc_word in skills.lower() for cnc_word in ['cnc', 'drehen', 'fräsen', 'maschinenbau']):
                    print("✅ GOOD: Showing CNC-related skills")
                else:
                    print("🤔 UNCLEAR: Skills don't match expected patterns")
            
            except Exception as e:
                print(f"❌ ERROR during extraction: {str(e)}")
        else:
            print(f"❌ File not found: {cv.filename}")
    
    print(f"\n" + "=" * 60)
    print("✅ Excel Export Simulation Completed")

if __name__ == "__main__":
    test_excel_export_simulation()
