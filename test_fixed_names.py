#!/usr/bin/env python3
"""
Test Fixed Name Extraction
==========================
Test that the name extraction is now working correctly.
"""

import glob
from hybrid_german_cv_parser import HybridGermanCVParser

def test_fixed_names():
    """Test the fixed name extraction"""
    print("🔧 Testing Fixed Name Extraction")
    print("=" * 50)
    
    parser = HybridGermanCVParser()
    
    # Test specific problematic CVs
    test_cases = [
        ('daniel', '<PERSON>'),
        ('werner', '<PERSON>'),
        ('frank', '<PERSON>'),
        ('pascal', '<PERSON>')
    ]
    
    for search_term, expected_name in test_cases:
        cv_files = [f for f in glob.glob('uploads/*.pdf') if search_term in f.lower()]
        if cv_files:
            test_file = cv_files[0]
            filename = test_file.split('\\')[-1]
            
            try:
                result = parser.extract_cv_data(test_file, ['name'])
                extracted_name = result.get('name', 'Not found')
                
                print(f"\n📄 {filename}")
                print(f"  Expected: {expected_name}")
                print(f"  Extracted: {extracted_name}")
                
                # Check if the extracted name is reasonable
                if extracted_name and extracted_name != 'Not found':
                    # Check if it contains the expected first name
                    expected_first = expected_name.split()[0].lower()
                    if expected_first in extracted_name.lower():
                        print(f"  ✅ GOOD: Contains expected first name")
                    else:
                        print(f"  ⚠️  DIFFERENT: Different from expected")
                    
                    # Check for problematic words
                    problematic_words = ['technische', 'leitung', 'telefon', 'mainburg', 'straße']
                    has_problematic = any(word in extracted_name.lower() for word in problematic_words)
                    
                    if has_problematic:
                        print(f"  ❌ PROBLEMATIC: Contains job title/location words")
                    else:
                        print(f"  ✅ CLEAN: No problematic words")
                else:
                    print(f"  ❌ FAILED: No name extracted")
            
            except Exception as e:
                print(f"  ❌ ERROR: {str(e)}")
        else:
            print(f"\n❌ No CV found for: {search_term}")
    
    print("\n" + "=" * 50)
    print("✅ Fixed Name Extraction Test Completed")

if __name__ == "__main__":
    test_fixed_names()
