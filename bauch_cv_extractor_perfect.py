"""
BAUCH CV Extractor Perfect
=========================
A wrapper around GermanCVParserPerfect that provides full compatibility with the BAUCH application
while delivering significantly improved field extraction accuracy for German CVs.

This module:
1. Integrates the perfect German CV parser
2. Maintains backward compatibility with existing BAUCH interfaces
3. Provides enhanced field mapping and validation
4. Includes comprehensive error handling and fallback mechanisms
"""

import os
import re
from typing import Dict, List, Optional
from german_cv_parser_perfect import GermanCVParserPerfect

class BAUCHCVExtractorPerfect:
    """
    Perfect CV extractor specifically designed for BAUCH application requirements.
    Combines the power of GermanCVParserPerfect with BAUCH-specific field mapping.
    """
    
    def __init__(self):
        """Initialize the perfect CV extractor"""
        try:
            self.perfect_parser = GermanCVParserPerfect()
            self.parser_available = True
            print("✅ Perfect German CV Parser initialized successfully")
        except Exception as e:
            print(f"⚠️ Perfect parser initialization failed: {e}")
            self.perfect_parser = None
            self.parser_available = False
    
    def extract_cv_data(self, file_path: str, fields: List[str] = None, candidate_name: str = None) -> Dict[str, str]:
        """
        Extract CV data with enhanced accuracy for German documents.

        Args:
            file_path: Path to the CV file (PDF, DOCX, or TXT)
            fields: List of fields to extract (name, email, phone, experience, skills, education)
            candidate_name: Optional candidate name from upload for validation

        Returns:
            Dictionary with extracted field data
        """
        if fields is None:
            fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']
        
        if not self.parser_available:
            return {"error": "Perfect parser not available"}
        
        try:
            # Use the perfect parser for extraction with candidate name validation
            result = self.perfect_parser.extract_cv_data(file_path, fields, candidate_name)
            
            # Enhanced post-processing for BAUCH requirements
            result = self._enhance_extraction_results(result, fields)
            
            # Validate and clean results
            result = self._validate_and_clean_results(result, fields)
            
            return result
            
        except Exception as e:
            return {"error": f"Extraction failed: {str(e)}"}
    
    def extract_cv_data_bilingual(self, file_path: str, fields: List[str] = None, candidate_name: str = None) -> Dict[str, str]:
        """
        Bilingual extraction method for compatibility with existing BAUCH code.
        Currently optimized for German CVs but handles English content as well.
        """
        return self.extract_cv_data(file_path, fields, candidate_name)
    
    def _enhance_extraction_results(self, result: Dict[str, str], fields: List[str]) -> Dict[str, str]:
        """
        Enhance extraction results with BAUCH-specific improvements.
        """
        enhanced = result.copy()
        
        # Enhanced name processing
        if 'name' in fields and 'name' in enhanced:
            enhanced['name'] = self._clean_name(enhanced['name'])
        
        # Enhanced experience processing
        if 'experience' in fields and 'experience' in enhanced:
            enhanced['experience'] = self._enhance_experience(enhanced['experience'])
        
        # Enhanced skills processing
        if 'skills' in fields and 'skills' in enhanced:
            enhanced['skills'] = self._enhance_skills(enhanced['skills'])
        
        # Enhanced education processing
        if 'education' in fields and 'education' in enhanced:
            enhanced['education'] = self._enhance_education(enhanced['education'])
        
        return enhanced
    
    def _clean_name(self, name: str) -> str:
        """Clean and validate extracted name"""
        if not name:
            return ""
        
        # Remove common prefixes/suffixes
        name = re.sub(r'^(Herr|Frau|Mr\.?|Mrs\.?|Ms\.?)\s+', '', name, flags=re.I)
        name = re.sub(r'\s+(Jr\.?|Sr\.?|III?|IV)$', '', name, flags=re.I)
        
        # Clean up spacing and capitalization
        name = ' '.join(word.capitalize() for word in name.split() if word.isalpha())
        
        return name.strip()
    
    def _enhance_experience(self, experience: str) -> str:
        """Enhance experience information"""
        if not experience:
            return "Berufserfahrung nicht spezifiziert"
        
        # If it's already in a good format, return as is
        if "Jahre" in experience or "years" in experience.lower():
            return experience
        
        # Try to extract years from various formats
        years_match = re.search(r'(\d+)\s*(?:Jahre?|years?)', experience, re.I)
        if years_match:
            years = years_match.group(1)
            return f"{years} Jahre Berufserfahrung"
        
        return experience
    
    def _enhance_skills(self, skills: str) -> str:
        """Enhance and validate skills list"""
        if not skills:
            return "Fähigkeiten nicht spezifiziert"
        
        # Split skills and clean them
        skill_list = [skill.strip() for skill in skills.split(',') if skill.strip()]
        
        # Remove duplicates while preserving order
        seen = set()
        unique_skills = []
        for skill in skill_list:
            skill_lower = skill.lower()
            if skill_lower not in seen and len(skill) > 1:
                seen.add(skill_lower)
                unique_skills.append(skill)
        
        # Limit to reasonable number of skills
        if len(unique_skills) > 20:
            unique_skills = unique_skills[:20]
        
        return ", ".join(unique_skills) if unique_skills else "Fähigkeiten nicht spezifiziert"
    
    def _enhance_education(self, education: str) -> str:
        """Enhance education information"""
        if not education or education == "Bildung nicht spezifiziert":
            return "Bildung nicht spezifiziert"
        
        # Clean up education entries
        education_parts = [part.strip() for part in education.split(',') if part.strip()]
        
        # Remove duplicates and clean
        unique_education = []
        seen = set()
        for part in education_parts:
            part_lower = part.lower()
            if part_lower not in seen:
                seen.add(part_lower)
                unique_education.append(part.capitalize())
        
        return ", ".join(unique_education) if unique_education else "Bildung nicht spezifiziert"
    
    def _validate_and_clean_results(self, result: Dict[str, str], fields: List[str]) -> Dict[str, str]:
        """
        Validate and clean all extraction results to ensure quality.
        """
        validated = {}
        
        for field in fields:
            if field in result:
                value = result[field].strip()
                
                # Field-specific validation
                if field == 'email':
                    validated[field] = self._validate_email(value)
                elif field == 'phone':
                    validated[field] = self._validate_phone(value)
                elif field == 'name':
                    validated[field] = self._validate_name(value)
                else:
                    validated[field] = value
            else:
                # Provide default values for missing fields
                validated[field] = self._get_default_value(field)
        
        return validated
    
    def _validate_email(self, email: str) -> str:
        """Validate email format"""
        if not email:
            return ""
        
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(email_pattern, email):
            return email.lower()
        
        return ""
    
    def _validate_phone(self, phone: str) -> str:
        """Validate and clean phone number"""
        if not phone:
            return ""
        
        # Remove all non-digit characters except + at the beginning
        cleaned = re.sub(r'[^\d+]', '', phone)
        
        # Ensure it looks like a valid phone number
        if len(cleaned) >= 7 and (cleaned.startswith('+') or cleaned.startswith('0')):
            return cleaned
        
        return ""
    
    def _validate_name(self, name: str) -> str:
        """Validate name format"""
        if not name:
            return ""
        
        # Check if it contains at least one alphabetic character
        if not re.search(r'[a-zA-ZäöüÄÖÜß]', name):
            return ""
        
        # Remove numbers and special characters
        cleaned = re.sub(r'[^a-zA-ZäöüÄÖÜß\s-]', '', name)
        cleaned = ' '.join(cleaned.split())  # Normalize whitespace
        
        return cleaned.strip()
    
    def _get_default_value(self, field: str) -> str:
        """Get default value for missing fields"""
        defaults = {
            'name': '',
            'email': '',
            'phone': '',
            'experience': 'Berufserfahrung nicht spezifiziert',
            'skills': 'Fähigkeiten nicht spezifiziert',
            'education': 'Bildung nicht spezifiziert'
        }
        return defaults.get(field, '')
    
    def get_parser_info(self) -> Dict[str, str]:
        """Get information about the parser"""
        return {
            "name": "BAUCH CV Extractor Perfect",
            "version": "1.0",
            "parser_available": str(self.parser_available),
            "features": [
                "Perfect German CV parsing",
                "Enhanced field validation",
                "BAUCH-specific optimizations",
                "Comprehensive error handling",
                "Bilingual compatibility"
            ]
        }


# Backward compatibility aliases
PerfectCVExtractor = BAUCHCVExtractorPerfect
BAUCHPerfectExtractor = BAUCHCVExtractorPerfect
