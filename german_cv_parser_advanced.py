"""
GermanCVParserAdvanced
----------------------
A **hybrid spaCy‑powered** resume/CV parser that works **offline** (once models are downloaded) and targets German documents.
It replaces the previous rule‑only version by adding:

* 💡 **spaCy `de_core_news_lg` (or `de_dep_news_trf`) NER** for PERSON, ORG, GPE, DATE
* 🏗️  **EntityRuler** patterns for domain‑specific fields (Kündigungsfrist, Staatsangehörigkeit, etc.)
* 📚 **Skill matcher** using a German skill vocabulary (list at bottom of file)
* 📅 **Experience extractor** that groups employment blocks via DATE entities and context heuristics, then computes total years + current employer
* 📈 **Seniority scoring** based on total experience years
* 📦 **All processing local** – only needs spaCy + dateutil + regex
"""

import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List

try:
    import spacy
    from dateutil.relativedelta import relativedelta
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    print("Warning: spaCy not available. Install with: pip install spacy python-dateutil")
    print("Then download German model: python -m spacy download de_core_news_lg")

# Load German spaCy model (large for better NER). Download once:   
#   python -m spacy download de_core_news_lg
if SPACY_AVAILABLE:
    try:
        NLP = spacy.load("de_core_news_lg")
    except OSError:
        try:
            NLP = spacy.load("de_core_news_sm")  # fallback
        except OSError:
            print("No German spaCy model found. Please install with:")
            print("python -m spacy download de_core_news_lg")
            NLP = None
else:
    NLP = None

EMAIL_RE  = re.compile(r"[\w\.-]+@[\w\.-]+", re.I)
PHONE_RE  = re.compile(r"\+?\d{2,3}[\s\-/]?(\(?0\d+\)?[\s\-/]?\d+|\d{3,})")
DATE_RANGE_RE = re.compile(r"(\d{4})\s*[–\-]\s*(\d{4}|heute|aktuell)")
BIRTH_RE  = re.compile(r"geb(?:\.|oren)?\s*am\s*(\d{1,2}[./-]\d{1,2}[./-]\d{2,4})", re.I)
NOTICE_RE = re.compile(r"K[üu]ndigungsfrist[:\s]+(.+)", re.I)
NATIONAL_RE = re.compile(r"Staatsangeh[öo]rigkeit[:\s]+([A-ZÄÖÜ][a-zäöüß]+)", re.I)
MARITAL_RE = re.compile(r"Familienstand[:\s]+([A-Za-zäöüß ]+)", re.I)
LANG_RE   = re.compile(r"Sprachen?[:\s]+(.+?)\s*(?:\n|$)")

# ▶️  Extended German skill list with CNC focus
GERMAN_SKILLS = {
    # CNC and Manufacturing skills (HIGH PRIORITY)
    "cnc", "cnc-programmierung", "cnc programmierung", "cnc-fräsen", "cnc fräsen",
    "cnc-drehen", "cnc drehen", "fanuc", "heidenhain", "siemens", "mazak", "okuma",
    "fräsen", "drehen", "bohren", "schleifen", "rüsten", "einfahren", "einrichten",
    "messtechnik", "qualitätskontrolle", "qualitätsprüfung", "erstbemusterung",
    "serienfertigung", "serienproduktion", "einzelfertigung", "kleinserie", "mittelserie",
    "cam", "cad", "mastercam", "powermill", "hypermill", "edgecam", "solidcam",
    "autocad", "solidworks", "catia", "inventor", "creo", "nx", "fusion 360",
    "werkzeugbau", "vorrichtungsbau", "lehren", "spannvorrichtungen",
    "koordinatenmesstechnik", "3d-messung", "zeiss", "mitutoyo", "werth",
    "iso 9001", "iso 14001", "ts 16949", "qualitätsmanagement", "qm-system",
    "lean", "lean manufacturing", "kaizen", "5s", "six sigma", "kontinuierliche verbesserung",

    # Technical skills
    "python", "java", "sql", "docker", "kubernetes", "aws",
    "javascript", "react", "c++", "c#", "sap", "scrum", "fem", "ansys",
    "matlab", "r", "tensorflow", "pytorch", "machine learning", "artificial intelligence",
    "data science", "big data", "hadoop", "spark", "tableau", "power bi", "excel", "powerpoint",
    "word", "outlook", "sharepoint", "teams", "slack", "jira", "confluence", "git", "github",
    "gitlab", "jenkins", "devops", "ci/cd", "agile", "kanban", "project management",
    
    # German technical terms
    "programmierung", "softwareentwicklung", "webentwicklung", "anwendungsentwicklung",
    "datenbank", "datenbankentwicklung", "algorithmus", "datenstrukturen", "objektorientiert",
    "funktional", "agile entwicklung", "versionskontrolle", "cloud computing", "microservices",
    "rest", "api", "json", "xml", "html", "css", "bootstrap", "jquery", "node.js", "express",
    "django", "flask", "spring", "spring boot", "hibernate", "angular", "vue", "laravel",
    "symfony", "mysql", "postgresql", "mongodb", "redis", "elasticsearch", "oracle",
    "microsoft sql server", "sqlite", "mariadb", "cassandra", "neo4j", "linux", "windows",
    "macos", "ubuntu", "centos", "debian", "bash", "powershell", "cmd", "terminal",
    
    # Manufacturing and engineering
    "cnc", "cam", "cad", "plc", "automation", "robotics", "lean", "six sigma", "iso 9001",
    "quality management", "process optimization", "continuous improvement", "kaizen",
    "manufacturing", "production", "assembly", "testing", "quality control", "inspection",
    "metrology", "measurement", "calibration", "documentation", "technical drawing",
    "mechanical engineering", "electrical engineering", "industrial engineering",
    
    # Soft skills in German
    "teamarbeit", "kommunikation", "führung", "projektmanagement", "problemlösung",
    "analytisches denken", "kreativität", "flexibilität", "belastbarkeit",
    "selbstständigkeit", "zuverlässigkeit", "verantwortungsbewusstsein",
    "kundenorientierung", "serviceorientierung", "qualitätsbewusstsein",
    "zeitmanagement", "organisationstalent", "präsentationsfähigkeiten",
    "verhandlungsgeschick", "konfliktlösung", "mentoring", "coaching"
}

class GermanCVParserAdvanced:
    """Hybrid NER + rule CV extractor for German resumes."""

    def __init__(self):
        self.nlp_available = NLP is not None
        if self.nlp_available:
            # Build EntityRuler to tag Kündigungsfrist etc.
            if "entity_ruler" not in NLP.pipe_names:
                ruler = NLP.add_pipe("entity_ruler", before="ner", config={"overwrite_ents": False})
                patterns = [
                    {"label": "NOTICE", "pattern": [{"LOWER": "kündigungsfrist"}]},
                    {"label": "NATIONAL", "pattern": [{"LEMMA": "staatsangehörigkeit"}]},
                    {"label": "LANG", "pattern": [{"LOWER": "sprachen"}]},
                ]
                ruler.add_patterns(patterns)

    # ---------- public API ----------
    def parse(self, text: str) -> Dict[str, str]:
        """Parse CV text and return structured data"""
        fields: Dict[str, str] = {k: "" for k in COLUMN_ORDER}
        text_norm = re.sub(r"\s+", " ", text.replace("–", "-")).strip()

        if self.nlp_available:
            doc = NLP(text_norm[:2000])  # parse first ~2k chars (enough for header & exp)

            # Name extraction – first PERSON entity
            person = next((ent.text for ent in doc.ents if ent.label_ == "PERSON"), "")
            if person:
                fields["Name"] = person
                name_parts = person.split()
                if name_parts:
                    fields["Vorname"] = name_parts[0]
        else:
            # Fallback name extraction without spaCy
            fields["Name"] = self._extract_name_fallback(text_norm)
            if fields["Name"]:
                fields["Vorname"] = fields["Name"].split()[0]

        # Emails / phones via regex
        email_match = EMAIL_RE.search(text_norm)
        fields["Email"] = email_match.group(0) if email_match else ""
        
        phone_match = PHONE_RE.search(text_norm)
        fields["Phone"] = phone_match.group(0) if phone_match else ""

        # Birthdate
        birth_match = BIRTH_RE.search(text_norm)
        if birth_match:
            fields["Geburtsdatum"] = birth_match.group(1)

        self._copy_group(NATIONAL_RE, text_norm, fields, "Staatsangehörigheit")
        self._copy_group(MARITAL_RE, text_norm, fields, "Familien-stand")
        self._copy_group(LANG_RE, text_norm, fields, "Sprache")
        self._copy_group(NOTICE_RE, text_norm, fields, "Kündigungsfrist")

        # Address extraction
        if self.nlp_available:
            self._extract_address(doc, text_norm, fields)
        else:
            self._extract_address_fallback(text_norm, fields)

        # Work experience blocks
        exps = self._extract_experience_blocks(text_norm)
        if exps:
            total_years = self._total_years(exps)
            fields["Berufserfahrung"] = f"{len(exps)} Positionen / {total_years:.0f} Jahre"
            fields["Vorherige AG"] = "; ".join(e["company"] for e in exps if e["company"])
            fields["Vorherige Arbeitsstellen"] = "; ".join(e["title"] for e in exps if e["title"])
            fields["aktueller AG"] = exps[0]["company"] if exps else ""
            fields["Beruf und Qualifikationen"] = "; ".join(e["title"] for e in exps if e["title"])

        # Skill extraction with improved filtering
        skill_hits = set()
        for skill in GERMAN_SKILLS:
            if re.search(rf"\b{re.escape(skill)}\b", text_norm, re.I):
                skill_hits.add(skill)

        # Filter out obvious non-skills (dates, addresses, etc.)
        filtered_skills = set()
        for skill in skill_hits:
            # Skip if it looks like a date
            if re.match(r'^\d{2}[./]\d{2}[./]\d{2,4}$', skill):
                continue
            # Skip if it looks like a year range
            if re.match(r'^\d{4}[-–]\d{4}$', skill):
                continue
            # Skip if it's just numbers
            if skill.isdigit():
                continue
            # Skip if it's too short and not a known abbreviation
            if len(skill) < 2 and skill.lower() not in ['r', 'c', 'c++', 'c#']:
                continue
            filtered_skills.add(skill)

        fields["Skills"] = ", ".join(sorted(filtered_skills))

        return fields

    # ---------- helpers ----------
    def _copy_group(self, pattern, text, fields, key):
        m = pattern.search(text)
        if m:
            fields[key] = m.group(1).strip()

    def _extract_name_fallback(self, text: str) -> str:
        """Fallback name extraction without spaCy"""
        lines = text.split('\n')[:15]  # Check first 15 lines
        for line in lines:
            line = line.strip()
            # Look for title-case names (2-3 words)
            if re.match(r'^[A-ZÄÖÜ][a-zäöüß]+\s+[A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)?$', line):
                # Exclude common CV headers and street names
                exclude_terms = [
                    'lebenslauf', 'curriculum', 'bewerbung', 'straße', 'str.', 'weg', 'allee', 'platz',
                    'gasse', 'ring', 'damm', 'ufer', 'berg', 'tal', 'feld', 'hof', 'markt'
                ]
                if not any(word in line.lower() for word in exclude_terms):
                    # Additional check: avoid lines with numbers (likely addresses)
                    if not re.search(r'\d', line):
                        return line
        return ""

    def _extract_address(self, doc, text, fields):
        """Extract address using spaCy doc"""
        # Use first PLZ + city pattern
        plz_match = re.search(r"(\d{5})\s+([A-ZÄÖÜ][a-zäöüß\- ]+)", text)
        if plz_match:
            fields["PLZ"], fields["Wohnort"] = plz_match.group(1), plz_match.group(2).strip()
        
        # Street: token sequence ending with Straße etc.
        for token in doc[:80]:
            if token.text.lower().endswith(("straße", "str.", "weg", "allee", "platz")):
                # Get surrounding context for full street address
                start_idx = max(0, token.i - 2)
                end_idx = min(len(doc), token.i + 2)
                span = doc[start_idx:end_idx]
                fields["Straße"] = span.text.strip()
                break

    def _extract_address_fallback(self, text: str, fields):
        """Fallback address extraction without spaCy"""
        # PLZ + city
        plz_match = re.search(r"(\d{5})\s+([A-ZÄÖÜ][a-zäöüß\- ]+)", text)
        if plz_match:
            fields["PLZ"], fields["Wohnort"] = plz_match.group(1), plz_match.group(2).strip()
        
        # Street
        street_match = re.search(r"([A-ZÄÖÜ][a-zäöüß\- ]+(?:straße|str\.|weg|allee|platz)\s*\d*)", text, re.I)
        if street_match:
            fields["Straße"] = street_match.group(1).strip()

    def _extract_experience_blocks(self, txt) -> List[Dict[str, str]]:
        """Extract work experience blocks"""
        blocks = []
        for m in DATE_RANGE_RE.finditer(txt):
            start, end = m.groups()
            # Get context after the date range
            span = txt[m.end(): m.end()+200]
            company = self._find_company(span)
            title = self._find_title(span)
            blocks.append({"start": start, "end": end, "company": company, "title": title})
        return blocks

    def _find_company(self, ctx):
        """Find company name in context"""
        # Look for German company suffixes
        for token in ctx.split():
            if any(suffix in token for suffix in ("GmbH", "AG", "KG", "UG", "SE", "e.V.", "mbH")):
                # Get the full company name (usually 1-3 words before the suffix)
                words = ctx.split()
                for i, word in enumerate(words):
                    if any(suffix in word for suffix in ("GmbH", "AG", "KG", "UG", "SE")):
                        # Take up to 3 words before + the suffix word
                        start_idx = max(0, i-2)
                        company_words = words[start_idx:i+1]
                        return " ".join(company_words).strip(',.;')
        return ""

    def _find_title(self, ctx):
        """Find job title in context"""
        # Look for pattern "Title bei Company"
        bei_match = re.search(r"([A-ZÄÖÜ][a-zäöüß]+(?: [A-ZÄÖÜ][a-zäöüß]+){0,3})\s+bei", ctx, re.I)
        if bei_match:
            return bei_match.group(1).strip()
        
        # Look for common job titles
        job_titles = [
            "entwickler", "manager", "leiter", "ingenieur", "techniker", "spezialist",
            "consultant", "analyst", "administrator", "koordinator", "assistent"
        ]
        for title in job_titles:
            title_match = re.search(rf"([A-ZÄÖÜ][a-zäöüß]*\s*{title})", ctx, re.I)
            if title_match:
                return title_match.group(1).strip()
        
        return ""

    def _total_years(self, exps):
        """Calculate total years of experience"""
        years = 0
        today = datetime.today().year
        for e in exps:
            try:
                start_year = int(e["start"])
                if e["end"] in ("heute", "aktuell"):
                    end_year = today
                else:
                    end_year = int(e["end"])
                years += max(0, end_year - start_year)
            except (ValueError, TypeError):
                continue
        return years

    # Compatibility methods for existing interface
    def extract_cv_data(self, file_path: str, fields: List[str] = None) -> Dict[str, str]:
        """Extract CV data from file (compatibility method)"""
        try:
            # Import here to avoid circular imports
            import fitz
            
            # Extract text from PDF
            with fitz.open(file_path) as doc:
                text = "\f".join(page.get_text("text") for page in doc)
            
            # Parse with advanced parser
            result = self.parse(text)
            
            # Map to expected field names
            mapped_result = {
                "name": result.get("Name", ""),
                "email": result.get("Email", ""),
                "phone": result.get("Phone", ""),
                "experience": result.get("Berufserfahrung", ""),
                "skills": result.get("Skills", ""),
                "education": result.get("Beruf und Qualifikationen", ""),
                "seniority": self._classify_seniority(result.get("Berufserfahrung", ""))
            }
            
            return mapped_result
            
        except Exception as e:
            return {"error": f"Could not process CV: {e}"}
    
    def _classify_seniority(self, experience_text: str) -> str:
        """Classify seniority based on experience"""
        if not experience_text:
            return "Junior"
        
        # Extract years from experience text
        years_match = re.search(r"(\d+)\s*Jahre", experience_text)
        if years_match:
            years = int(years_match.group(1))
            if years >= 7:
                return "Senior"
            elif years >= 3:
                return "Mid"
            else:
                return "Junior"
        
        return "Junior"

# Column order (for dataframe/export)
COLUMN_ORDER = [
    "Name", "Vorname", "Geburtsdatum", "Straße", "PLZ", "Wohnort", "Staatsangehörigheit",
    "Familien-stand", "Sprache", "Beruf und Qualifikationen", "Berufserfahrung", "Vorherige AG",
    "Vorherige Arbeitsstellen", "Kündigungsfrist", "aktueller AG", "Notizen", "Eingang",
    "Absage Bewerber", "Absage Bauch", "Zusage", "Arbeitsvertrag HSP", "Arbeitsvertrag CNC", 
    "Skills", "Email", "Phone"
]

# Backward compatibility
GermanCVParser = GermanCVParserAdvanced
