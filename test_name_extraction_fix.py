#!/usr/bin/env python3
"""
Test Name Extraction Fix
========================
Test the improved name extraction to ensure it doesn't pick up job titles or city names.
"""

import glob
from german_cv_parser_perfect import GermanCVParserPerfect
from hybrid_german_cv_parser import HybridGermanCVParser

def test_name_extraction():
    """Test name extraction with the improved parser"""
    print("🔧 Testing Name Extraction Fix")
    print("=" * 50)
    
    # Test both parsers
    perfect_parser = GermanCVParserPerfect()
    hybrid_parser = HybridGermanCVParser()
    
    # Test with all CVs
    cv_files = glob.glob('uploads/*.pdf')
    
    print(f"📁 Testing {len(cv_files)} CV files:")
    
    problematic_names = []
    good_names = []
    
    for cv_file in cv_files:
        filename = cv_file.split('\\')[-1]
        
        try:
            # Test perfect parser
            perfect_result = perfect_parser.extract_cv_data(cv_file, ['name'])
            perfect_name = perfect_result.get('name', 'Not found')
            
            # Test hybrid parser (what BAUCH actually uses)
            hybrid_result = hybrid_parser.extract_cv_data(cv_file, ['name'])
            hybrid_name = hybrid_result.get('name', 'Not found')
            
            print(f"\n📄 {filename}")
            print(f"  Perfect: {perfect_name}")
            print(f"  Hybrid:  {hybrid_name}")
            
            # Check for problematic patterns
            problematic_words = [
                'technische', 'leitung', 'management', 'direktor', 'manager',
                'mainburg', 'telefon', 'email', 'straße', 'str', 'adresse',
                'messprogrammen', 'programmierung', 'entwicklung', 'abteilung'
            ]
            
            # Check hybrid result (what BAUCH uses)
            is_problematic = False
            if hybrid_name and hybrid_name != 'Not found':
                for word in problematic_words:
                    if word in hybrid_name.lower():
                        is_problematic = True
                        break
            
            if is_problematic:
                print(f"  ❌ PROBLEMATIC: Contains job title/location words")
                problematic_names.append((filename, hybrid_name))
            elif hybrid_name and hybrid_name != 'Not found' and len(hybrid_name.split()) >= 2:
                print(f"  ✅ GOOD: Looks like a proper name")
                good_names.append((filename, hybrid_name))
            else:
                print(f"  ⚠️  NO NAME FOUND")
        
        except Exception as e:
            print(f"  ❌ ERROR: {str(e)}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    print(f"✅ Good names: {len(good_names)}")
    for filename, name in good_names[:5]:  # Show first 5
        print(f"   {filename}: {name}")
    if len(good_names) > 5:
        print(f"   ... and {len(good_names) - 5} more")
    
    print(f"\n❌ Problematic names: {len(problematic_names)}")
    for filename, name in problematic_names:
        print(f"   {filename}: {name}")
    
    if len(problematic_names) == 0:
        print("\n🎉 SUCCESS: No problematic names found!")
    else:
        print(f"\n⚠️  ISSUE: {len(problematic_names)} CVs still have problematic name extraction")
    
    print("=" * 50)

if __name__ == "__main__":
    test_name_extraction()
