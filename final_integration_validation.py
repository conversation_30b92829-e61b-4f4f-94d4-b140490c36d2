#!/usr/bin/env python3
"""
Final Integration Validation & Report
=====================================
Comprehensive validation of the perfect CV parser integration with BAUCH application.

This script:
1. Validates all components are working
2. Tests with multiple CV files
3. Compares accuracy improvements
4. Generates a comprehensive report
5. Provides recommendations for deployment
"""

import os
import sys
import glob
import time
from typing import Dict, List, Tuple
from datetime import datetime

# Import all components
try:
    from german_cv_parser_perfect import GermanCVParserPerfect
    from bauch_cv_extractor_perfect import BAUCHCVExtractorPerfect
    from hybrid_german_cv_parser import HybridGermanCVParser
    from app import bilingual_extractor, hr_db
    ALL_COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"❌ Component import failed: {e}")
    ALL_COMPONENTS_AVAILABLE = False

class FinalIntegrationValidator:
    """Final validation of perfect CV parser integration"""
    
    def __init__(self):
        """Initialize validator"""
        self.test_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']
        self.results = {
            'total_tests': 0,
            'successful_tests': 0,
            'accuracy_improvements': [],
            'performance_metrics': {},
            'issues_found': [],
            'recommendations': []
        }
    
    def run_final_validation(self):
        """Run comprehensive final validation"""
        print("\n" + "="*80)
        print("🏁 FINAL INTEGRATION VALIDATION & REPORT")
        print("="*80)
        print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if not ALL_COMPONENTS_AVAILABLE:
            print("❌ Not all components available for validation")
            return
        
        # Validation steps
        print("\n🔍 Step 1: Component Validation")
        self.validate_components()
        
        print("\n🔍 Step 2: Accuracy Testing")
        self.test_accuracy_improvements()
        
        print("\n🔍 Step 3: Performance Testing")
        self.test_performance_metrics()
        
        print("\n🔍 Step 4: Real-world Testing")
        self.test_real_world_scenarios()
        
        print("\n🔍 Step 5: BAUCH Application Testing")
        self.test_bauch_integration()
        
        # Generate final report
        print("\n📊 Generating Final Report...")
        self.generate_final_report()
    
    def validate_components(self):
        """Validate all components are working correctly"""
        print("Validating components...")
        
        components = {
            'Perfect Parser': GermanCVParserPerfect(),
            'BAUCH Perfect Extractor': BAUCHCVExtractorPerfect(),
            'Enhanced Hybrid Parser': HybridGermanCVParser(),
            'BAUCH App Extractor': bilingual_extractor
        }
        
        for name, component in components.items():
            try:
                # Test basic functionality
                if hasattr(component, 'get_parser_info'):
                    info = component.get_parser_info()
                    print(f"  ✅ {name}: {info.get('name', 'Unknown')}")
                else:
                    print(f"  ✅ {name}: Available")
                
                # Test extraction method
                if hasattr(component, 'extract_cv_data') or hasattr(component, 'extract_cv_data_bilingual'):
                    print(f"    ✅ Extraction method available")
                else:
                    print(f"    ❌ No extraction method")
                    self.results['issues_found'].append(f"{name}: No extraction method")
            
            except Exception as e:
                print(f"  ❌ {name}: Error - {str(e)[:50]}...")
                self.results['issues_found'].append(f"{name}: {str(e)}")
    
    def test_accuracy_improvements(self):
        """Test accuracy improvements with sample CVs"""
        print("Testing accuracy improvements...")
        
        # Create test CV content
        test_cv_content = """
        Max Mustermann
        CNC-Programmierer
        
        Email: <EMAIL>
        Telefon: +49 123 456789
        Adresse: Hauptstraße 123, 12345 Berlin
        
        Berufserfahrung:
        2020-heute CNC-Programmierer bei TechCorp GmbH
        2018-2020 Maschinenbediener bei MetallWorks
        
        Ausbildung:
        2016-2018 Ausbildung Zerspanungsmechaniker
        
        Fähigkeiten: CNC-Programmierung, SolidWorks, Fanuc, Siemens
        """
        
        # Test with different extractors
        extractors = {
            'Perfect Parser': GermanCVParserPerfect(),
            'Enhanced Hybrid': HybridGermanCVParser()
        }
        
        expected_fields = {
            'name': 'Max Mustermann',
            'email': '<EMAIL>',
            'phone': '+49 123 456789'
        }
        
        for extractor_name, extractor in extractors.items():
            try:
                # Create temp file
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                    f.write(test_cv_content)
                    temp_file = f.name
                
                # Extract data
                if hasattr(extractor, 'extract_cv_data'):
                    result = extractor.extract_cv_data(temp_file, self.test_fields)
                elif hasattr(extractor, 'parse_text'):
                    result = extractor.parse_text(test_cv_content)
                    # Map to BAUCH format
                    result = self._map_perfect_results(result)
                
                # Calculate accuracy
                correct_fields = 0
                total_fields = len(expected_fields)
                
                for field, expected in expected_fields.items():
                    extracted = result.get(field, '').lower().strip()
                    if expected.lower() in extracted:
                        correct_fields += 1
                
                accuracy = (correct_fields / total_fields) * 100
                print(f"  📊 {extractor_name}: {accuracy:.1f}% accuracy ({correct_fields}/{total_fields})")
                
                self.results['accuracy_improvements'].append({
                    'extractor': extractor_name,
                    'accuracy': accuracy,
                    'correct_fields': correct_fields,
                    'total_fields': total_fields
                })
                
                # Clean up
                os.unlink(temp_file)
            
            except Exception as e:
                print(f"  ❌ {extractor_name}: Error - {str(e)[:50]}...")
    
    def test_performance_metrics(self):
        """Test performance metrics"""
        print("Testing performance metrics...")
        
        # Find test files
        cv_files = glob.glob("uploads/*.pdf")[:3]  # Test with first 3 files
        
        if not cv_files:
            print("  ❌ No CV files found for performance testing")
            return
        
        extractors = {
            'Perfect Parser': BAUCHCVExtractorPerfect(),
            'Enhanced Hybrid': HybridGermanCVParser()
        }
        
        for extractor_name, extractor in extractors.items():
            total_time = 0
            successful_extractions = 0
            
            for cv_file in cv_files:
                try:
                    start_time = time.time()
                    
                    if hasattr(extractor, 'extract_cv_data_bilingual'):
                        result = extractor.extract_cv_data_bilingual(cv_file, self.test_fields)
                    else:
                        result = extractor.extract_cv_data(cv_file, self.test_fields)
                    
                    extraction_time = time.time() - start_time
                    total_time += extraction_time
                    
                    if 'error' not in result:
                        successful_extractions += 1
                
                except Exception:
                    pass
            
            avg_time = total_time / len(cv_files) if cv_files else 0
            success_rate = (successful_extractions / len(cv_files)) * 100 if cv_files else 0
            
            print(f"  ⚡ {extractor_name}: {avg_time:.3f}s avg, {success_rate:.1f}% success")
            
            self.results['performance_metrics'][extractor_name] = {
                'avg_time': avg_time,
                'success_rate': success_rate,
                'total_files': len(cv_files)
            }
    
    def test_real_world_scenarios(self):
        """Test real-world scenarios with uploaded CVs"""
        print("Testing real-world scenarios...")
        
        cv_files = glob.glob("uploads/*.pdf")
        
        if not cv_files:
            print("  ❌ No real CV files found")
            return
        
        print(f"  📁 Testing with {len(cv_files)} real CV files")
        
        extractor = bilingual_extractor  # Use the actual BAUCH extractor
        successful_extractions = 0
        field_extraction_stats = {field: 0 for field in self.test_fields}
        
        for cv_file in cv_files[:5]:  # Test first 5 files
            try:
                result = extractor.extract_cv_data_bilingual(cv_file, self.test_fields)
                
                if 'error' not in result:
                    successful_extractions += 1
                    
                    # Count successful field extractions
                    for field in self.test_fields:
                        value = result.get(field, '').strip()
                        if value and 'nicht spezifiziert' not in value and 'not specified' not in value:
                            field_extraction_stats[field] += 1
                
                self.results['total_tests'] += 1
            
            except Exception as e:
                self.results['issues_found'].append(f"Real-world test error: {str(e)}")
        
        self.results['successful_tests'] = successful_extractions
        
        print(f"  📊 Success rate: {(successful_extractions/min(5, len(cv_files)))*100:.1f}%")
        print(f"  📋 Field extraction rates:")
        for field, count in field_extraction_stats.items():
            rate = (count / min(5, len(cv_files))) * 100
            print(f"    {field}: {rate:.1f}%")
    
    def test_bauch_integration(self):
        """Test integration with BAUCH application"""
        print("Testing BAUCH application integration...")
        
        try:
            # Test database connection
            jobs = hr_db.get_all_jobs()
            print(f"  ✅ Database: {len(jobs)} jobs accessible")
            
            # Test extractor integration
            info = bilingual_extractor.get_parser_info()
            print(f"  ✅ Extractor: {info.get('name', 'Unknown')}")
            print(f"  ✅ Perfect Parser Active: {info.get('perfect_parser_available', 'Unknown')}")
            
            # Test with a database CV if available
            if jobs:
                for job in jobs:
                    cvs = hr_db.get_cvs_for_job(job.title)
                    if cvs:
                        print(f"  ✅ CV Access: {len(cvs)} CVs for '{job.title}'")
                        break
        
        except Exception as e:
            print(f"  ❌ BAUCH integration error: {str(e)[:50]}...")
            self.results['issues_found'].append(f"BAUCH integration: {str(e)}")
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        print("\n" + "="*80)
        print("📋 FINAL INTEGRATION REPORT")
        print("="*80)
        
        # Summary
        print(f"\n📊 SUMMARY")
        print(f"Total Tests: {self.results['total_tests']}")
        print(f"Successful Tests: {self.results['successful_tests']}")
        if self.results['total_tests'] > 0:
            success_rate = (self.results['successful_tests'] / self.results['total_tests']) * 100
            print(f"Overall Success Rate: {success_rate:.1f}%")
        
        # Accuracy improvements
        if self.results['accuracy_improvements']:
            print(f"\n🎯 ACCURACY RESULTS")
            for result in self.results['accuracy_improvements']:
                print(f"{result['extractor']}: {result['accuracy']:.1f}% accuracy")
        
        # Performance metrics
        if self.results['performance_metrics']:
            print(f"\n⚡ PERFORMANCE METRICS")
            for extractor, metrics in self.results['performance_metrics'].items():
                print(f"{extractor}: {metrics['avg_time']:.3f}s avg, {metrics['success_rate']:.1f}% success")
        
        # Issues found
        if self.results['issues_found']:
            print(f"\n⚠️  ISSUES FOUND")
            for issue in self.results['issues_found']:
                print(f"  • {issue}")
        else:
            print(f"\n✅ NO CRITICAL ISSUES FOUND")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS")
        
        if not self.results['issues_found']:
            print("  ✅ Perfect CV parser integration is ready for production deployment")
            print("  ✅ All components are working correctly")
            print("  ✅ BAUCH application compatibility confirmed")
        
        print("  📈 Consider monitoring field extraction accuracy in production")
        print("  🔧 Fine-tune skill keywords based on your specific domain requirements")
        print("  📊 Monitor performance with larger CV volumes")
        
        print(f"\n🎉 INTEGRATION VALIDATION COMPLETED SUCCESSFULLY!")
        print("="*80)
    
    def _map_perfect_results(self, perfect_result: Dict) -> Dict:
        """Map perfect parser results to BAUCH format"""
        mapped = {}
        
        # Map name
        first_name = perfect_result.get("Vorname", "").strip()
        last_name = perfect_result.get("Name", "").strip()
        if first_name and last_name:
            mapped['name'] = f"{first_name} {last_name}"
        elif first_name or last_name:
            mapped['name'] = first_name or last_name
        else:
            mapped['name'] = ""
        
        # Direct mappings
        mapped['email'] = perfect_result.get("Email", "")
        mapped['phone'] = perfect_result.get("Phone", "")
        mapped['experience'] = perfect_result.get("Berufserfahrung", "")
        mapped['skills'] = perfect_result.get("Skills", "")
        mapped['education'] = perfect_result.get("Beruf und Qualifikationen", "")
        
        return mapped


def main():
    """Main validation function"""
    print("🏁 Starting Final Integration Validation...")
    
    validator = FinalIntegrationValidator()
    validator.run_final_validation()


if __name__ == "__main__":
    main()
