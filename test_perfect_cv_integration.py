#!/usr/bin/env python3
"""
Perfect CV Integration Test Suite
=================================
Comprehensive testing of the new perfect CV parser integration with BAUCH application.

This test suite:
1. Tests the perfect parser directly
2. Tests the enhanced hybrid parser
3. Compares results with existing extractors
4. Validates BAUCH application compatibility
5. Tests with real CV files from uploads directory
"""

import os
import sys
import glob
from typing import Dict, List, Tuple
from pathlib import Path

# Import all extractors for comparison
try:
    from german_cv_parser_perfect import GermanCVParserPerfect
    PERFECT_PARSER_AVAILABLE = True
except ImportError as e:
    print(f"❌ Perfect parser not available: {e}")
    PERFECT_PARSER_AVAILABLE = False

try:
    from bauch_cv_extractor_perfect import BAUCHCVExtractorPerfect
    BAUCH_PERFECT_AVAILABLE = True
except ImportError as e:
    print(f"❌ BAUCH perfect extractor not available: {e}")
    BAUCH_PERFECT_AVAILABLE = False

try:
    from hybrid_german_cv_parser import HybridGermanCVParser
    HYBRID_AVAILABLE = True
except ImportError as e:
    print(f"❌ Hybrid parser not available: {e}")
    HYBRID_AVAILABLE = False

try:
    from bilingual_cv_extractor import BilingualCVExtractor
    BILINGUAL_AVAILABLE = True
except ImportError as e:
    print(f"❌ Bilingual extractor not available: {e}")
    BILINGUAL_AVAILABLE = False

class PerfectCVIntegrationTester:
    """Comprehensive tester for perfect CV parser integration"""
    
    def __init__(self):
        """Initialize all available extractors"""
        self.extractors = {}
        
        if PERFECT_PARSER_AVAILABLE:
            self.extractors['perfect'] = GermanCVParserPerfect()
            print("✅ Perfect parser loaded")
        
        if BAUCH_PERFECT_AVAILABLE:
            self.extractors['bauch_perfect'] = BAUCHCVExtractorPerfect()
            print("✅ BAUCH perfect extractor loaded")
        
        if HYBRID_AVAILABLE:
            self.extractors['hybrid'] = HybridGermanCVParser()
            print("✅ Enhanced hybrid parser loaded")
        
        if BILINGUAL_AVAILABLE:
            self.extractors['bilingual'] = BilingualCVExtractor()
            print("✅ Bilingual extractor loaded")
        
        self.test_fields = ['name', 'email', 'phone', 'experience', 'skills', 'education']
    
    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("\n" + "="*80)
        print("🧪 PERFECT CV PARSER INTEGRATION TEST SUITE")
        print("="*80)
        
        # Test 1: Basic functionality
        print("\n📋 Test 1: Basic Functionality")
        self.test_basic_functionality()
        
        # Test 2: Real CV files
        print("\n📋 Test 2: Real CV Files from Uploads")
        self.test_real_cv_files()
        
        # Test 3: Comparison with existing extractors
        print("\n📋 Test 3: Extractor Comparison")
        self.test_extractor_comparison()
        
        # Test 4: BAUCH application compatibility
        print("\n📋 Test 4: BAUCH Application Compatibility")
        self.test_bauch_compatibility()
        
        # Test 5: Error handling
        print("\n📋 Test 5: Error Handling")
        self.test_error_handling()
        
        print("\n" + "="*80)
        print("✅ ALL TESTS COMPLETED")
        print("="*80)
    
    def test_basic_functionality(self):
        """Test basic functionality of all extractors"""
        print("Testing basic functionality...")
        
        for name, extractor in self.extractors.items():
            print(f"\n🔍 Testing {name} extractor:")
            
            try:
                # Test with a simple text CV
                if hasattr(extractor, 'parse_text'):
                    # Test perfect parser's parse_text method
                    test_text = """
                    Max Mustermann
                    Ingenieur für Maschinenbau
                    Email: <EMAIL>
                    Telefon: +49 123 456789
                    
                    Berufserfahrung:
                    2020-2024 CNC-Programmierer bei Bauch GmbH
                    2018-2020 Maschinenbediener bei TechCorp
                    
                    Fähigkeiten: CNC, Programmierung, SolidWorks, Python
                    """
                    result = extractor.parse_text(test_text)
                    print(f"  ✅ parse_text method works: {len(result)} fields extracted")
                
                # Test with extract_cv_data method (BAUCH compatibility)
                if hasattr(extractor, 'extract_cv_data'):
                    # This would need a real file, so we'll test the method exists
                    print(f"  ✅ extract_cv_data method available")
                
                # Get parser info if available
                if hasattr(extractor, 'get_parser_info'):
                    info = extractor.get_parser_info()
                    print(f"  ℹ️  Parser: {info.get('name', 'Unknown')}")
                    print(f"  ℹ️  Version: {info.get('version', 'Unknown')}")
                
            except Exception as e:
                print(f"  ❌ Error testing {name}: {e}")
    
    def test_real_cv_files(self):
        """Test with real CV files from uploads directory"""
        print("Testing with real CV files...")
        
        # Find CV files in uploads directory
        upload_dir = "uploads"
        if not os.path.exists(upload_dir):
            print(f"❌ Upload directory '{upload_dir}' not found")
            return
        
        cv_files = []
        for ext in ['*.pdf', '*.docx', '*.doc']:
            cv_files.extend(glob.glob(os.path.join(upload_dir, ext)))
        
        if not cv_files:
            print("❌ No CV files found in uploads directory")
            return
        
        print(f"📁 Found {len(cv_files)} CV files")
        
        # Test with first few files
        test_files = cv_files[:3]  # Test with first 3 files
        
        for file_path in test_files:
            filename = os.path.basename(file_path)
            print(f"\n📄 Testing with: {filename}")
            
            for name, extractor in self.extractors.items():
                if hasattr(extractor, 'extract_cv_data'):
                    try:
                        result = extractor.extract_cv_data(file_path, self.test_fields)
                        
                        if 'error' in result:
                            print(f"  ❌ {name}: {result['error']}")
                        else:
                            extracted_count = sum(1 for v in result.values() if v and v.strip())
                            print(f"  ✅ {name}: {extracted_count}/{len(self.test_fields)} fields extracted")
                            
                            # Show key fields
                            if result.get('name'):
                                print(f"    👤 Name: {result['name'][:30]}...")
                            if result.get('email'):
                                print(f"    📧 Email: {result['email']}")
                    
                    except Exception as e:
                        print(f"  ❌ {name} error: {str(e)[:50]}...")
    
    def test_extractor_comparison(self):
        """Compare results between different extractors"""
        print("Comparing extractor results...")
        
        # Find a test file
        upload_dir = "uploads"
        cv_files = glob.glob(os.path.join(upload_dir, "*.pdf"))
        
        if not cv_files:
            print("❌ No PDF files found for comparison")
            return
        
        test_file = cv_files[0]
        filename = os.path.basename(test_file)
        print(f"📄 Comparing results for: {filename}")
        
        results = {}
        for name, extractor in self.extractors.items():
            if hasattr(extractor, 'extract_cv_data'):
                try:
                    result = extractor.extract_cv_data(test_file, self.test_fields)
                    results[name] = result
                except Exception as e:
                    results[name] = {"error": str(e)}
        
        # Compare results
        print("\n📊 Comparison Results:")
        print("-" * 60)
        
        for field in self.test_fields:
            print(f"\n🔍 {field.upper()}:")
            for name, result in results.items():
                value = result.get(field, 'N/A') if 'error' not in result else 'ERROR'
                print(f"  {name:15}: {str(value)[:40]}...")
    
    def test_bauch_compatibility(self):
        """Test compatibility with BAUCH application interface"""
        print("Testing BAUCH application compatibility...")
        
        # Test the hybrid parser (what BAUCH actually uses)
        if 'hybrid' in self.extractors:
            hybrid = self.extractors['hybrid']
            
            # Test bilingual extraction method
            if hasattr(hybrid, 'extract_cv_data_bilingual'):
                print("  ✅ extract_cv_data_bilingual method available")
            
            # Test parser info
            if hasattr(hybrid, 'get_parser_info'):
                info = hybrid.get_parser_info()
                print(f"  ℹ️  Primary engine: {info.get('primary_engine', 'Unknown')}")
                print(f"  ℹ️  Perfect parser: {info.get('perfect_parser_available', 'Unknown')}")
            
            print("  ✅ BAUCH compatibility confirmed")
        else:
            print("  ❌ Hybrid parser not available")
    
    def test_error_handling(self):
        """Test error handling with invalid files"""
        print("Testing error handling...")
        
        # Test with non-existent file
        for name, extractor in self.extractors.items():
            if hasattr(extractor, 'extract_cv_data'):
                try:
                    result = extractor.extract_cv_data("nonexistent.pdf", self.test_fields)
                    if 'error' in result:
                        print(f"  ✅ {name}: Properly handles missing files")
                    else:
                        print(f"  ⚠️  {name}: No error for missing file")
                except Exception:
                    print(f"  ✅ {name}: Exception handling works")


def main():
    """Main test function"""
    print("🚀 Starting Perfect CV Parser Integration Tests...")
    
    # Check if we have the required modules
    if not any([PERFECT_PARSER_AVAILABLE, BAUCH_PERFECT_AVAILABLE, HYBRID_AVAILABLE]):
        print("❌ No extractors available for testing!")
        return
    
    # Run tests
    tester = PerfectCVIntegrationTester()
    tester.run_comprehensive_tests()


if __name__ == "__main__":
    main()
